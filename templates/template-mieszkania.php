<?php
/**
 * Template Name: Mieszkania
 */

global $wp;

$levels_to_display = [
    'PARTER' => 'mieszkania/parter/',
    'PIĘTRO I' => 'mieszkania/pietro-i/',
    'PIĘTRO II' => 'mieszkania/pietro-ii/',
    'PIĘTRO III' => 'mieszkania/pietro-iii/',
    'PIĘTRO IV' => 'mieszkania/pietro-iv/',
    'PIĘTRO V' => 'mieszkania/pietro-v/',
    'GARAŻ PODZIEMNY' => 'mieszkania/garaz-podziemny/',
];
$currentUrl = $wp->request;

get_header();
?>
<main id="main-content" class="single">
    <div class="container">
        <section class="mieszkania-template">
            <h1 class="section-title">Sprawdź dostępne <span class="highlight">mieszkania</span> w przekroju pięter</h1>
            <div class="pricing">
                <div class="block">
                    <li class="info">
                        <span class="name">Komórki lokatorskie na kondygnacjach:</span>
                        <span class="price">6 000,00 zł/m²</span>
                    </li>
                </div>
                <div class="block">
                    <li class="info">
                        <span class="name">Komórki lokatorskie w garażu:</span>
                        <span class="price">5 000,00 zł/m²</span>
                    </li>
                </div>
                <div class="block">
                    <li class="info">
                        <span class="name">Miejsce postojowe zewnętrzne:</span>
                        <span class="price">15 000,00 zł</span>
                    </li>
                </div>
                <div class="block">
                    <li class="info">
                        <span class="name">Miejsce postojowe w garażu podziemnym:</span>
                        <span class="price">35 000,00 zł</span>
                    </li>
                </div>
            </div>
            <div class="left-right" id="rzuty">
                <div class="left">
                    <span class="title">Wybierz kondygnację:</span>
                    <div class="floor-selection">
                        <?php foreach ($levels_to_display as $name => $url): ?>
                            <a class="button <?php if (trim($currentUrl, '/') !== trim($url, '/')) echo 'reverse'; ?>" href="<?php echo esc_url(home_url($url . '#rzuty')); ?>"><?php echo $name ?></a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="right">
                    <div class="caption">
                        <div class="legend">
                            <span class="legend-title">Legenda:</span>
                            <div class="legend-item dostepne">
                                <span class="color-box"></span>
                                <span class="legend-text">Dostępne</span>
                            </div>
                            <div class="legend-item rezerwacja">
                                <span class="color-box"></span>
                                <span class="legend-text">Rezerwacja</span>
                            </div>
                            <div class="legend-item sprzedane">
                                <span class="color-box"></span>
                                <span class="legend-text">Sprzedane</span>
                            </div>
                        </div>
                    </div>
                    <div class="floor-plan">
                        <img class="compass" src="<?php echo get_template_directory_uri(); ?>/public/compass.svg"
                            alt="Kompas">
                            <?php
                            if (function_exists('draw_mieszkania')) {
                                draw_mieszkania(get_post_meta(get_the_ID(), 'mieszkania_floor', true));
                            }

                            $shortcode = get_post_meta(get_the_ID(), 'mieszkania_shortcode', true);
                            if (!empty($shortcode)) {
                                echo do_shortcode($shortcode);
                            }
                            ?>
                    </div>
                </div>

        </section>
        <?php get_template_part( 'templates/view/featured-mieszkania', get_post_format() ); ?>
    </div>
    <?php get_template_part( 'templates/view/cta-section', get_post_format() ); ?>
</main>

<?php get_footer(); ?>