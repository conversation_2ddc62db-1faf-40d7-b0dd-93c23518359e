<?php
global $wp;
$currentUrl = esc_url(home_url($wp->request));
$contactUrl = esc_url(home_url('kontakt'));
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="profile" href="http://gmpg.org/xfn/11">
    <title><?php wp_title('-', true, 'right'); ?></title>
    <?php if (function_exists('the_custom_logo')) {
        $custom_logo_id = get_theme_mod('custom_logo');
        if ($custom_logo_id) {
            $logo_url = wp_get_attachment_image_src($custom_logo_id, 'full');
        }
    } ?>
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <div class="mobile-container">
        <a href="/" class="logo-container">
            <img src="<?php echo $logo_url[0]; ?>" alt="Logo <?php echo get_bloginfo( 'name' ); ?>" />
        </a>
        <div class="right">
            <?php echo do_shortcode('[dd_clipboard_count]');  ?>
            <div class="hamburger-toggle-container">
                <div class="hamburger-toggle">
                    <div class="hamburger-toggle-bar"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="menu-container">
        <div class="container">
            <div class="menu">
                <nav class="main-navigation">
                    <?php
                        wp_nav_menu(
                            array(
                                'theme_location' => 'main-menu',
                                'menu_id' => 'main-menu',
                                'container' => 'ul',
                                'menu_class' => 'main-menu',
                                'link_after' => '<span  class="dropdown-icon"><svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 1L5 5L1 1" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/></svg></span>',
                                )
                            );
					?>
                </nav>
                <a href="<?php echo esc_url(home_url()); ?>" class="logo-container">
                    <img src="<?php echo $logo_url[0]; ?>" alt="Logo <?php echo get_bloginfo( 'name' ); ?>" />
                </a>
                <div class="right">
                    <a href="<?php echo $contactUrl ?>" class="button reverse <?php if ($currentUrl === $contactUrl) echo 'active'; ?>">Kontakt</a>
                    <?php echo do_shortcode('[dd_clipboard_count]');  ?>
                </div>
            </div>
        </div>
    </div>
