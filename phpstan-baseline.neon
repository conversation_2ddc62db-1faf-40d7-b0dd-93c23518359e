parameters:
	ignoreErrors:
		-
			message: '#^Call to function is_int\(\) with int\<0, max\> will always evaluate to true\.$#'
			identifier: function.alreadyNarrowedType
			count: 1
			path: includes/Repository/RealEstateRepository.php

		-
			message: '#^Offset ''komorka\-lokatorska''\|''lokal\-uslugowy''\|''miejsce\-postojowe…''\|''mieszkanie'' on array\{mieszkanie\: ''Mieszkanie'', miejsce\-postojowe\-garaz\: ''Miejsce postojowe \-…'', miejsce\-postojowe\-na\-zewnatrz\: ''Miejsce postojowe \-…'', komorka\-lokatorska\: ''Komórka lokatorska'', lokal\-uslugowy\: ''Lokal usługowy''\} on left side of \?\? always exists and is not nullable\.$#'
			identifier: nullCoalesce.offset
			count: 1
			path: includes/Repository/RealEstateRepository.php

		-
			message: '#^Using nullsafe property access "\?\-\>value" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: includes/post-type.php
