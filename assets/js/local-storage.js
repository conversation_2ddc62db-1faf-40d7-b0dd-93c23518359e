document.addEventListener("DOMContentLoaded", function () {
    const clipboardKey = "clipboard";
    const clipboardList = document.getElementById("dd_clipboard-list");
    const clearClipboardButton = document.getElementById("clear-clipboard-button");

    const getClipboard = () => JSON.parse(localStorage.getItem(clipboardKey)) || [];
    const saveClipboard = (clipboard) => localStorage.setItem(clipboardKey, JSON.stringify(clipboard));

    const updateClipboardCount = () => {
        const clipboard = getClipboard();
        const count = clipboard.length;

        document.querySelectorAll("#dd_clipboard-count").forEach(el => {
            if (el.closest("#mieszkania-clipboard")) {
                el.innerHTML = `(<span>${count}</span>)`;
            } else {
                el.textContent = count;
            }
        });
    };

    const transformRooms = (rooms) => {
        if (rooms == 1) return `${rooms} pokój`;
        if (rooms >= 2 && rooms <= 4) return `${rooms} pokoje`;
        return `${rooms} pokoi`;
    };

    const updateClipboardUI = () => {
        updateClipboardCount();
        if (clipboardList) {
            const clipboard = getClipboard();
            if (clipboard.length === 0) {
                clipboardList.innerHTML = "<p>Schowek jest pusty.</p>";
            }
        }
    };

    const renderClipboard = async () => {
        if (clipboardList) {
            const clipboard = getClipboard();
            if (clipboard.length === 0) {
                clipboardList.innerHTML = "<p>Schowek jest pusty.</p>";
            } else {
                const ids = clipboard.map(item => item.id).join(",");
                try {
                    const response = await fetch(`/wp-json/dd/v2/realestate/?list_of_ids=${ids}`);
                    const realEstateData = await response.json();
                    if (!Array.isArray(realEstateData)) {
                        throw new Error("Błąd: Niepoprawne dane z API.");
                    }

                    clipboardList.innerHTML = "";

                    clipboard.forEach(item => {
                        const realEstate = realEstateData.find(re => Number(re.id) === Number(item.id));
                        if (realEstate) {
                            renderClipboardItem(item, realEstate);
                        }
                    });
                } catch (error) {
                    console.error("Błąd pobierania danych:", error);
                }
            }
        }
    };

    const renderClipboardItem = (item, realEstate) => {
        const container = document.createElement("div");
        container.classList.add("mieszkanie");
        container.id = `${item.id}`;
        
        const placeholder = matchTypeToPlaceholder(realEstate.type);
        const imageUrl = realEstate.angled_shot || realEstate.vertical_shot || placeholder;

        let contentClass = realEstate.rooms > 0 ? '' : 'with-rooms';
        const imageClass = imageUrl === placeholder ? "placeholder" : "property-image";
    
        container.innerHTML = `
            <a href="${realEstate.url}" class="property-link">
                ${imageUrl ? `<img src="${imageUrl}" alt="${realEstate.id_number}" class="${imageClass}">` : ""}
                <h3 class="title">${realEstate.id_number}</h3>
                <div class="content ${contentClass}">
                    <div class="top">
                       <div class="info">
                           <div>
                                ${realEstate.size > 0 ? `<span class="square">${realEstate.size} m²</span>` : ""}
                                ${realEstate.rooms > 0 ? `<span class="rooms">• ${transformRooms(realEstate.rooms)}</span>` : ""}
                           </div>
                           <div>
                                ${
                                    realEstate.floor > -2
                                        ? `<${realEstate.rooms > 0 ? 'span' : 'li'} class="floor"><span>${realEstate.floor == 0 ? 'Parter' :
                                         realEstate.floor == -1 ? 'garaż' : `${realEstate.floor} piętro`}</span></${realEstate.rooms > 0 ? 'span' : 'li'}>` : ""
                                }
                           </div>
                        </div>
                        <p class="status ${realEstate.status}">• ${realEstate.status_display_name}</p>
                    </div>
                    <div class="bottom">
                        <div class="price">
                            <span class="price-label">Cena:</span> 
                            <span class="price-value">${parseFloat(realEstate.price).toLocaleString('pl-PL')} zł</span>
                            <span class="price-valuemkw">${parseFloat(realEstate.pricemkw).toLocaleString('pl-PL')} zł / m²</span>
                        </div>
                    </div>
                </div>
            </a>
        `;
        
        const removeButton = document.createElement("button");
        removeButton.classList.add("dd_remove-from-clipboard");
        removeButton.setAttribute("title", "Usuń ze schowka");
        removeButton.innerHTML = `
            <svg width="45" height="45" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.5" y="0.5" width="47" height="47" rx="23.5" stroke="#003B7B"/>
                <path d="M18 18L30.7279 30.7279" stroke="#181715" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M31.0005 18L18.2726 30.7279" stroke="#181715" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        `;
        removeButton.addEventListener("click", () => removeFromClipboard(item.id));
    
        container.appendChild(removeButton);
    
        container.classList.add("fade-in");
        container.addEventListener('animationend', () => {
            container.classList.remove("fade-in");
        });
    
        clipboardList.appendChild(container);
    };

    const matchTypeToPlaceholder = (type) => {
        switch(type) {
            case 'komórka_lokatorska': return DD_PLACEHOLDER.storage;
            case 'miejsce_postojowe': return DD_PLACEHOLDER.parking;
            case 'mieszkanie':
            default: return DD_PLACEHOLDER.flat;
        }
    };

    const addToClipboard = (id) => {
        const clipboard = getClipboard();
        if (!clipboard.some(item => item.id === id)) {
            clipboard.push({ id });
            saveClipboard(clipboard);
            renderClipboard();
            updateClipboardCount();
        }
    };

    const removeFromClipboard = (id) => {
        let clipboard = getClipboard().filter(item => item.id !== id);
        saveClipboard(clipboard);

        const itemElement = document.getElementById(`${id}`);
        if (itemElement) {
            itemElement.classList.add("fade-out");
            setTimeout(() => {
                itemElement.remove();
                updateClipboardUI();
            }, 300);
        } else {
            updateClipboardUI();
        }
    };

    const clearClipboard = () => {
        localStorage.removeItem(clipboardKey);
        if (clipboardList) clipboardList.innerHTML = "<p>Schowek został wyczyszczony.</p>";
        updateClipboardCount();
    };

    clearClipboardButton?.addEventListener("click", clearClipboard);

    document.querySelectorAll(".dd_add-to-clipboard").forEach((button) => {
        button.addEventListener("click", () => addToClipboard(button.id));
    });

    renderClipboard();
    updateClipboardCount();
});