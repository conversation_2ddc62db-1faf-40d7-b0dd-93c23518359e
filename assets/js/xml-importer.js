jQuery(document).ready(function($) {
    $('#start-import').on('click', function() {
        $('#dd-status').text('Rozpoczynam import...');
        $.post(xmlImporterAjax.ajax_url, {
            action: 'xml_importer_import',
            nonce: xmlImporterAjax.nonce
        }, function(response) {
            if (response.success) {
                $('#dd-status').text('Import zakończony');
                $('#in-xml').text(response.data.inXml);
                $('#skipped').text(response.data.skipped);
                $('#failed').text(response.data.failed);
                $('#import-result').show();
            } else {
                $('#dd-status').text('Błąd importowania');
                alert('Błąd importu: ' + response.data.message);
            }
        })
            .fail(function (){
                $('#dd-status').text('Błąd importowania');
                alert('Błąd importu: ' + response.data);
            })
    });
});
