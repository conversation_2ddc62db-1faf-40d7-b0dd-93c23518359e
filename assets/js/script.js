document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".dd_add-to-clipboard").forEach(function (button) {
        button.addEventListener("click", function (event) {
            event.preventDefault();
            event.stopPropagation();

            this.classList.add("added");
            this.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12l5 5L19 7" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `;

            setTimeout(() => {
                this.classList.remove("added");
                this.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.22 1.41606C10.8579 1.13727 10.4113 0.990877 9.95444 1.00125C9.49758 1.01162 9.05805 1.17813 8.709 1.47306L1.709 7.47206C1.4868 7.65986 1.30824 7.89387 1.18579 8.15778C1.06333 8.42169 0.99993 8.70913 1 9.00006V18.0001C1 18.5305 1.21071 19.0392 1.58579 19.4143C1.96086 19.7894 2.46957 20.0001 3 20.0001H17C17.5304 20.0001 18.0391 19.7894 18.4142 19.4143C18.7893 19.0392 19 18.5305 19 18.0001V10.6461M13 5.00006H19M16 2.00006V8.00006" stroke="#181715" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }, 3000);
        });
    });
});

document.addEventListener("DOMContentLoaded", function () {
    const mainImage = document.querySelector(".mieszkanie-single .main-image img");
    const thumbnails = document.querySelectorAll(".gallery .thumbnail");

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener("click", function () {
            mainImage.classList.add("fade-out");
            setTimeout(() => {
                mainImage.src = this.querySelector("img").src;
                mainImage.classList.remove("fade-out");
            }, 100);

            thumbnails.forEach(thumbnail => thumbnail.classList.remove("active"));
            this.classList.add("active");
        });
    });

    if (thumbnails.length > 0) {
        thumbnails[0].classList.add("active");
    }
});

(function($) {
    $(document).ready(function() {
        // Create modal HTML
        $('body').append(`
            <div class="dd-3d-tour-modal-overlay">
                <div class="dd-3d-tour-modal">
                    <span class="dd-3d-tour-modal-close"><svg fill="none" height="21" viewBox="0 0 20 21" width="20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="m10 20.6008c5.5228 0 10-4.4772 10-10 0-5.52288-4.4772-10.000031-10-10.000031-5.52285 0-10 4.477151-10 10.000031 0 5.5228 4.47715 10 10 10zm-2.40409-13.53557c-.31242-.31242-.81895-.31242-1.13137 0s-.31242.81895 0 1.13137l2.4042 2.4042-2.40417 2.4042c-.31242.3124-.31242.8189 0 1.1313.31242.3125.81895.3125 1.13137 0l2.40416-2.4041 2.4041 2.4041c.3125.3124.819.3124 1.1314 0s.3124-.819 0-1.1314l-2.4041-2.4041 2.4041-2.40416c.3125-.31242.3125-.81895 0-1.13137-.3124-.31242-.8189-.31242-1.1313 0l-2.4042 2.40416z" fill="#e3e3e3" fill-rule="evenodd"/></svg></span>
                    <iframe class="dd-3d-tour-modal-iframe" src="" allowfullscreen></iframe>
                </div>
            </div>
        `);

        const $overlay = $('.dd-3d-tour-modal-overlay');
        const $iframe = $('.dd-3d-tour-modal-iframe');
        const $closeBtn = $('.dd-3d-tour-modal-close');

        $('.walkaround[data-3d-link]').on('click', function() {
            const threeDLink = $(this).data('3d-link');

            if (threeDLink) {
                $iframe.attr('src', threeDLink);
                $overlay.addClass('is-open');
            }
        });

        // Close modal on close button click
        $closeBtn.on('click', function() {
            $overlay.removeClass('is-open');
            $iframe.attr('src', '');
        });

        // Close modal when clicking outside
        $overlay.on('click', function(e) {
            if (e.target === this) {
                $overlay.removeClass('is-open');
                $iframe.attr('src', '');
            }
        });
    });
})(jQuery);

(function($) {
    $(document).ready(function() {
        const $overlay = $('.dd-price-history-modal-overlay');
        const $closeBtn = $('.dd-price-history-modal-close');

        $('#show-price-history').on('click', function() {
            $overlay.addClass('is-open');
        });

        $closeBtn.on('click', function() {
            $overlay.removeClass('is-open');
        });

        $overlay.on('click', function(e) {
            if (e.target === this) {
                $overlay.removeClass('is-open');
            }
        });
    });
})(jQuery);