jQuery(document).ready(function ($) {
    // Obsługuje kliknięcia na linki paginacji
    $(document).on('click', '.pagination a', function (e) {
        e.preventDefault(); // Zapobiega domyślnemu działaniu linku

        var href = $(this).attr('href');
        var page = href.match(/page\/(\d+)/) ? href.match(/page\/(\d+)/)[1] : 1; // Pobiera numer strony z URL

        $.ajax({
            url: my_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'load_more_posts',
                paged: page,
                nonce: my_ajax_object.nonce
            },
            success: function (response) {
                if (response.success) {
                    $('.mieszkania-list').html(response.data.posts); // Aktualizacja postów
                    $('.pagination').html(response.data.pagination); // Aktualizacja paginacji
                    $('.posts-count').html(response.data.posts_count); // Aktualizacja liczby wyświetlanych postów

                    $('html, body').animate({ scrollTop: 0 }, 'fast');
                } else {
                    console.log('Błąd: ' + response.data);
                }
            },
            error: function (xhr, status, error) {
                console.error('Błąd AJAX: ', error);
            }
        });
    });
});

(function () {
    var hamburger = {
        bgToggle: document.querySelector(".hamburger-toggle-container"),
        navToggle: document.querySelector(".hamburger-toggle"),
        sidebar: document.querySelector(".menu-container"),

        doToggle: function (e) {
            e.preventDefault();
            this.bgToggle.classList.toggle("expanded");
            this.navToggle.classList.toggle("expanded");
            this.sidebar.classList.toggle("menu-opened");
        },
    };

    hamburger.bgToggle.addEventListener("click", function (e) {
        hamburger.doToggle(e);
    });
})();

document.addEventListener("DOMContentLoaded", () => {
    const label = document.querySelector('label[for="agreement"]');
    const checkbox = document.querySelector('input[name="agreement"]');

    label?.addEventListener('click', () => checkbox.checked = !checkbox.checked);
});

$(document).ready(function () {
    const $menu = $('.menu-container');

    $(window).scroll(function () {
        if ($(window).width() >= 991) {
            const isScrolled = $(this).scrollTop() > 50;
            $menu.toggleClass('scrolled', isScrolled);
        } else {
            $menu.removeClass('scrolled');
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.tab');
    const imageSets = document.querySelectorAll('.image-set');

    function handleTabClick(event) {
        const tab = event.target;
        const year = tab.getAttribute('data-year');
        const activeImageSet = document.querySelector(`.image-set[data-year="${year}"]`);

        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        imageSets.forEach(set => set.classList.remove('active', 'fade-in'));

        if (activeImageSet) {
            activeImageSet.classList.add('active', 'fade-in');
        }
    }

    tabs.forEach(tab => tab.addEventListener('click', handleTabClick));
});

document.addEventListener('DOMContentLoaded', function () {
    const rzutySection = document.getElementById('rzuty');
    if (rzutySection && window.location.hash === '#rzuty') {
        rzutySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
});