#main-content {
  &.single {
    margin-top: 30px;
  }
}

.mieszkania-template {
  padding-bottom: 5rem;
  .section-title {
    text-align: center;
  }

  .pricing {
    display: flex;
    justify-content: space-between;
    padding: 1rem 0;

    .block {
      display: flex;

      &:not(:first-child)::before {
        content: "•";
        display: block;
        text-align: center;
        margin: 0 1rem;
        font-size: 2rem;
        color: $color-black;
      }

      .info {
        display: flex;
        flex-direction: column;
        text-align: center;
        align-items: center;
        list-style-type: none;

        .price {
          font-weight: bold;
          white-space: pre;
        }
      }
    }
  }

  .left-right {
    padding-top: 5rem;

    .left {
      display: flex;
      flex-direction: column;
      flex-basis: calc(25% - 10px);
      gap: 15px;

      .title {
        font-weight: 400;
      }

      .floor-selection {
        display: flex;
        flex-direction: column;
        gap: 20px;

        button {
          .reverse {
            .active {
              background: $color-border;
            }
          }
        }

        a {
          text-align: center;
        }
      }
    }

    .right {
      flex: 1 1 0;

      .caption {
        display: flex;
        justify-content: flex-end;

        .legend {
          display: flex;
          gap: 30px;
          align-items: center;

          .legend-title {
            font-weight: 700;
            font-size: $font-size-16px;
          }

          .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;

            .legend-text {
              font-size: $font-size-16px;
            }

            .color-box {
              width: 15px;
              height: 15px;
              border-radius: 50%;
            }

            &.dostepne {
              .color-box {
                background-color: #38894a;
              }
            }

            &.rezerwacja {
              .color-box {
                background-color: #d79729;
              }
            }

            &.sprzedane {
              .color-box {
                background-color: #c53521;
              }
            }
          }
        }
      }

      .floor-plan {
        position: relative;

        .compass {
          position: absolute;
          bottom: 0;
          left: 0;
        }

        .hotspots-image-container,
        .leaflet-container {
          background: transparent !important;
        }
        .hotspots-container {
          .hotspots-image-container {
            .hotspots-image {
              position: relative;
              max-width: 700px !important;
              object-fit: contain;
              aspect-ratio: 1 / 1;
              width: 100%;
            }
          }
        }
      }
    }
  }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
  .mieszkania-template {
    padding-bottom: 3rem;
    .pricing {
      flex-direction: column;
      align-items: center;
      .block {
        display: block;

          .info {
            flex-basis: 100%;
            text-align: center;
            list-style-type: disc;
          }
     
      }
    }

    .left-right {
      padding-top: 3rem;

      .left {
        flex-basis: 100%;
        .floor-selection {
          flex-direction: row;
          flex-wrap: wrap;
          .button {
            flex-basis: calc(50% - 10px);
            &:last-of-type {
              flex-basis: 100%;
            }
          }
        }
      }

      .right {
        overflow-x: hidden;
        .caption {
          justify-content: center;
          .legend {
            flex-wrap: wrap;
            gap: 5px;

            .legend-title {
              margin-right: 10px;
            }
          }
        }

        .floor-plan {
          .compass {
            right: 0;
            left: unset;
            max-width: 60px;
          }
        }
      }
    }
  }
}
@media (max-width: $media-mobile) {
}
@media (max-width: $media-mobile-sm) {
}
