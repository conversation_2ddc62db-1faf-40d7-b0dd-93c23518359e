@charset "UTF-8";
@import "_variables";

#tagsdiv-typ,
#tagsdiv-udogodnienia,
#statusdiv {
  display: none;
}

#mieszkanie_details {
  .inside {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .taxonomies {
    display: flex;
    flex-direction: column;
    gap: 5px;
    position: relative;

    ul {
      margin: 0;
    }
    label {
      margin-left: 5px;
    }
  }

  .images {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    padding: 1rem 0;

    .button {
      width: fit-content;
      height: fit-content;
    }

    #vertical_preview, #angled_preview {
      display: flex;
      flex-direction: column;
      align-items: start;
      gap: 30px;
    }
  }
}

#wpbody-content {
  .left-right {
    display: flex;
    gap: 20px;

    .left {
      flex-basis: calc(50% - 20px);

      .form-table {
        td {
          padding: 0;
        }

        .select2 {
          width: 100% !important;
          max-width: 100%;
        }
      }
    }

    .right {
      flex: 1;
    }
  }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
  #wpbody-content {
    .left-right {
      flex-direction: column;
      .left {
        flex-basis: 100%;
      }
    }
  }
}
@media (max-width: $media-mobile) {
  .images {
    #vertical_preview, #angled_preview {
      flex-direction: column;
    }
  }
}
@media (max-width: $media-mobile-sm) {
}
