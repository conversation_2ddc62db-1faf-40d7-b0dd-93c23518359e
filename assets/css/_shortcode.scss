.mieszkanie-single {
  .left-right {
    .left {
      .main-image {
        .placeholder-text {
          color: $color-black;
          font-size: $font-size-16px;
        }
      }

      .gallery {
        .thumbnail,
        .walkaround {
          border: 1px solid $color-border;
          .title {
            color: $color-black;
          }
        }
        .thumbnail {
          transition: $transition;
          &.active {
            transition: $transition;
            box-shadow: 0px 0px 19.9px rgb(0, 59, 123, 0.6);
          }
        }
      }
    }
    .right {
      .info {
        .status {
          font-weight: 700;
        }

        .link-pdf {
          background: $color-primary;
          color: $color-white;
          transition: $transition;

          &:hover {
            background: $color-button-hover;
            transition: $transition;
          }

          &.reverse {
            background: $color-white;
            border: 1px solid $color-border;
            color: $color-black;

            &:hover {
              background: $color-hover;
              transition: $transition;
            }
          }
        }
      }
    }
  }
}

.mieszkaniaSwiper {
  .mieszkanie {
    transition: $transition;
    &:hover,
    &:focus {
      transition: $transition;
      background: $color-hover;
    }
  }
}

.mieszkanie {
  .property-link {
    .title {
      font-family: $font-secondary;
      color: $color-primary;
    }

    .bottom {
      .price {
        .price-value {
          color: #181715;
        }
      }
    }
  }
}

.mieszkania-list {
  button {
    background: $color-border;
  }
}

#mieszkania-clipboard {
  .dd_clipboard-title {
    .column {
      h1,
      #dd_clipboard-count {
        font-size: $font-size-48px;
        font-family: $font-primary;

        span {
          color: $color-primary;
        }
      }
    }

    button {
      font-family: $font-secondary;
      color: #292929cc;
      font-size: $font-size-18px;

      &:hover {
        font-weight: 500;
      }
    }
  }

  #dd_clipboard-list {
    .mieszkanie {
      border: 1px solid $color-border;
      transition: $transition;

      &:hover,
      &:focus {
        transition: $transition;
        box-shadow: 0px 0px 19.9px rgb(0, 59, 123, 0.6);
      }

      button {
        background: none;
        width: fit-content;

        svg:hover {
          > rect {
            fill: $color-button-hover;
            border-color: $color-button-hover;  
          }

          path {
            stroke: $color-white;
          }
        }
      }

      &.fade-out {
        opacity: 0;
        transform: scale(0.8);
        transition: opacity 0.3s ease-out, transform 0.3s ease-out;
      }
    }
  }
}

.mieszkaniaSwiper,
.mieszkanie-single {
  button {
    background: $color-primary;

    &:hover,
    &:focus {
      background: $color-button-hover;
    }
  }
}

.leaflet-rrose-content-wrapper {
  border: 1px solid $color-primary !important;
  border-radius: 0 !important;

  .leaflet-rrose-content {
    .mieszkanie-tooltip {
      font-family: $font-secondary;
      font-weight: 400;
      .row {
        &:first-of-type {
          font-weight: 700;

          &.sprzedane {
            color: #c53521;
          }

          &.dostepne {
            color: #38894a;
          }

          &.rezerwacja {
            color: #d79729;
          }
        }
      }

      .button {
        color: $color-white;
      }
    }
  }
}

.swiper-button-next, .swiper-button-prev {
  &:hover, &:focus {
    border-radius: 50% !important;
    background: $color-hover !important;
  }
}

.dd_add-to-clipboard {
  svg {
    path {
      stroke: $color-white;
    }
  }
}

.dd_clipboard-button {
  border: 1px solid $color-primary;
  background: $color-primary;

  &:hover,
  &:focus,
  &:active {
    border-color: $color-button-hover;
    background: $color-button-hover;
  }

  .clipboard-status {
    color: #FFFFFF;
  }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
}
@media (max-width: $media-mobile) {
  .mieszkanie-single {
    .left-right {
      .left {
        .main-image {
          .placeholder-text {
            font-size: 12px;
            line-height: 1.5;
          }
        }
      }
    }
  }
}
@media (max-width: $media-mobile-sm) {
}
