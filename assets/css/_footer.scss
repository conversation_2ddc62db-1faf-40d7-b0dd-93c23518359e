footer {
  margin-top: 5rem;

  body:not(.page-template-template-kontakt) & {
    margin: 0;
  }

  .footer-container {
    background-color: $color-primary;
    color: $color-white;
    padding: 5rem 0;

    .container {
      display: flex;
      flex-direction: column;
      gap: 50px;

      a {
        text-decoration: none;
        color: $color-white;

        &:hover,
        &:focus {
          text-decoration: underline;
        }
      }

      .left-right {
        gap: 35px;
        .info {
          align-items: center;
          width: 100%;
          &.left-right {
            min-height: 210px;
            max-width: 400px;

            .left {
              display: flex;
              flex-direction: column;
              gap: 25px;
              height: 100%;

              a {
                display: flex;
              }

              .bottom {
                display: flex;
                gap: 25px;
                align-items: center;

                .social-links {
                  transition: $transition;
                  &:hover {
                    transform: scale(1.1);
                    transition: $transition;
                  }
                  img {
                    max-width: 30px;
                  }
                }
              }
            }
          }
        }

        .menus {
          display: flex;
          flex-wrap: wrap;
          color: $color-white;
          font-family: $font-secondary;
          font-size: $font-size-16px;
          font-style: normal;
          font-weight: 400;
          line-height: 1.5;
          width: 100%;
          gap: 15px;

          .flex {
            display: flex;
            justify-content: space-between;
            width: 100%;

            .footer-menu {
              display: flex;
              flex-basis: calc(60% * 1 / 2 - 10px);
              justify-content: center;
              align-items: flex-start;
              gap: 20px;

              &:first-of-type {
                flex-basis: calc(40% - 10px);
              }

              img {
                max-width: 45px;
              }

              .content {
                display: flex;
                flex-direction: column;
                gap: 10px;

                .footer-menu {
                  &-2,
                  &-3,
                  &-4 {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                  }
                }
              }
            }
          }

          .menu-title {
            font-size: $font-size-20px;
            font-weight: 500;
          }

          .footer-menu {
            word-break: break-word;
            width: 100%;
          }
        }
        li {
          padding-bottom: 16px;
        }
      }
    }
  }

  .footer-bottom {
    .footer-menu {
      display: flex;
      justify-content: space-between;

      .footer-menu-5 {
        display: flex;
        gap: 25px;
      }
    }
  }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
  footer {
    .footer-container {
      .container {
        .left-right {
          flex-wrap: wrap;
          gap: 0;

          .info {
            &.left-right {
              max-width: 100%;
            }
          }

          .menus {
            .flex {
              .footer-menu {
                flex-basis: calc(100% * 1 / 3 - 10px);

                &:first-of-type {
                  flex-basis: calc(100% * 1 / 3 - 10px);
                }
              }
            }
          }
        }
      }
    }
  }
}
@media (max-width: $media-tablet) {
}
@media (max-width: $media-mobile) {
  footer {
    .footer-container {
      .container {
        .left-right {
          gap: 15px;

          .menus {
            gap: 35px;

            .flex {
              flex-direction: column;
              gap: 25px;

              .footer-menu {
                flex-basis: 100%;
                justify-content: flex-start;

                &:first-of-type {
                  flex-basis: 100%;
                }
              }
            }
          }
        }

        .footer-bottom {
          .footer-menu {
            flex-direction: column-reverse;
            gap: 35px;

            .footer-menu {
              &-5 {
                justify-content: space-between;
                flex-wrap: wrap;
              }
            }
          }
        }
      }
    }
  }
}
@media (max-width: $media-mobile-sm) {
}
