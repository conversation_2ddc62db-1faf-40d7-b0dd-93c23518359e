.section-contact {
  padding: 2rem 0 5rem 0;
  .left-right {
    .left {
      padding: 3rem 0;
      flex-basis: calc(50% - 25px);

      .menus {
        display: flex;
        flex-wrap: wrap;
        color: #4d4c46;
        font-family: $font-secondary;
        font-size: $font-size-16px;
        font-style: normal;
        font-weight: 400;
        line-height: 1.5;
        width: 100%;
        gap: 15px;

        .footer-menu {
          .footer-menu-1 {
            padding: 3rem 0;
          }
        }

        .flex {
          display: flex;
          flex-direction: column;
          gap: 35px;
          max-width: 75%;

          .footer-menu {
            display: flex;
            align-items: flex-start;
            gap: 15px;

            img {
              max-width: 60px;
            }

            .content {
              display: flex;
              flex-direction: column;
              gap: 10px;

              .footer-menu {
                &-2,
                &-3,
                &-4 {
                  display: flex;
                  flex-direction: column;
                  gap: 10px;
                }
              }
            }
          }
        }

        .menu-title {
          font-size: $font-size-18px;
          font-weight: 700;
          color: #181715;
        }

        .footer-menu {
          word-break: break-word;
          width: 100%;
        }
      }
    }
    .right {
      flex: 1;
      background-color: $color-primary;
      padding: 60px 60px 30px 60px;
    }
  }
}

.section-map {
  .left-right {
    .left {
      flex-basis: calc(45% - 25px);
      align-content: center;
    }
    .right {
      flex: 1;

      iframe {
        width: 100%;
      }
    }
  }
}

.wpcf7 form.invalid .wpcf7-response-output {
  border-color: rgb(220, 50, 50);
}

.wpcf7 {
  &-response-output {
    font-size: $font-size-16px;
    color: $color-white;
  }

  textarea,
  input,
  select {
    font-family: $font-secondary;
    font-size: $font-size-16px;
    border-radius: 5px;
  }

  &-two-columns {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  &-column {
    width: calc(50% - 10px);

    input {
      width: 100%;
      padding: 15px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
  }

  &-full-width {
    width: 100%;

    a {
      color: $color-white;
      
      &:hover, &:focus {
        text-decoration: underline;
      }
    }

    p {
      font-size: $font-size-14px;
      color: $color-white;
    }

    .wpcf7-form-control-wrap {
      .wpcf7-list-item {
        display: flex;
        gap: 10px;
        margin: 0;

        span {
          font-size: $font-size-14px;
          font-weight: 300;
          color: $color-white;
        }
      }
    }

    textarea {
      width: 100%;
      padding: 15px;
      border: 1px solid #ccc;
      border-radius: 5px;
      height: 120px;
      resize: none;
      transition: height 0.5s ease;

      &:focus {
        height: 150px;
        transition: height 0.5s ease;
      }
    }

    input[type="submit"] {
      font-weight: 700;
      width: 100%;
      background-color: $color-hover;
      color: $color-black;
      text-transform: uppercase;
      padding: 12px;
      border: none;
      cursor: pointer;
      transition: $transition;

      &:hover {
        background-color: $color-button-hover;
        transition: $transition;
      }
    }
  }

  .wpcf7-checkbox {
    input[type="checkbox"] {
      position: absolute;
      opacity: 0;
      width: 20px;
      height: 20px;
      top: 0;
      left: 0;
      z-index: 999;

      + span {
        border-radius: 0;
        cursor: pointer;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 20px;
          height: 20px;
          border: 1px solid #dfd8bb;
          border-radius: 5px;
          background: transparent;
        }

        &::after {
          content: "\01F5F8";
          position: absolute;
          top: 0;
          left: 2px;
          width: 20px;
          height: 20px;
          font-size: 22px;
          text-align: center;
          line-height: 28px;
          color: $color-black;
          visibility: hidden;
        }
      }

      &:checked + span::before {
        background: $color-white;
      }

      &:checked + span::after {
        visibility: visible;
      }

      &:not(:checked) + span::after {
        visibility: hidden;
      }
    }
  }

  .wpcf7-list-item-label {
    margin-left: 40px;
    display: inline-block;
  }
}

@media (max-width: $media-tablet) {
  .section-contact {
    padding-top: 0;
    .left-right {
      .left {
        flex-basis: 100%;
        padding: 0 0 1rem 0;
        
        .section-title {
          text-align: center;
        }
        .menus {
          .flex {
            max-width: 100%;
          }
        }
      }
      .right {
        padding: 25px;
      }
    }
  }

  .section-map {
    .left-right {
      flex-direction: column;
      gap: 50px;

      .section-title {
        text-align: center;
      }
      
      .left {
        flex-basis: 100%;
      }
    }
  }

  .wpcf7 {
    &-column {
      width: 100%;
    }

    .wpcf7-list-item-label {
      margin-left: 30px;
    }
  }

}
@media (max-width: $media-mobile) {
}
@media (max-width: $media-mobile-sm) {
}
