.mobile-container {
  position: fixed;
  z-index: 1200;
  background-color: $color-white;
  display: none;
  top: 0;
  width: 100%;
  transition: $transition;
  height: $mobile-nav-height;
  justify-content: space-between;

  a.logo-container {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 15px;
    flex-grow: 1;

    img {
      max-height: 100%;
      width: auto;
      max-width: 70%;
    }
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
  }

  .hamburger-toggle-container {
    height: 100%;
    width: auto;
    transition: $transition;
    text-decoration: none;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;

    .hamburger-toggle {
      user-select: none;
      cursor: pointer;
      height: 25px;
      width: 25px;
      margin: 0 15px;
      display: flex;
      align-items: center;

      &-bar,
      &-bar:before,
      &-bar:after {
        position: absolute;
        transition: $transition;
        background: $color-black;
        content: '';
        height: 3px;
        width: 25px;

      }
      &-bar {
        margin-top: 0px;
        &:before{
          margin-top: -7px;
          left: 0;
        }
        &:after {
          margin-top: 7px;
          left: 0;
        }
      }

      &.expanded .hamburger-toggle-bar {
        background-color: transparent;

        &:before,
        &:after {
          transform: rotate(-45deg);
          margin-top: 0;
        }

        &:before {
          transform: rotate(45deg);
          margin-top: 0;
        }
      }
    }
  }
}




@media (max-width: $media-hd) {}
@media (max-width: $media-laptop) {}
@media (max-width: $media-tablet) {
  .mobile-container {
    display: flex;
  }
}
@media (max-width: $media-mobile) {}
@media (max-width: $media-mobile-sm) {}