.homepage-content {
  min-height: 90vh;
  background-image: url("../../public/hallera_front.webp");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top;

  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding-top: 60px;
    padding-bottom: 30px;

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 25px;

      .title {
        font-size: $font-size-56px;
        line-height: 1.5em;
        text-align: center;
      }

      .sub-title {
        font-family: $font-secondary;
        font-size: $font-size-18px;
        font-weight: 400;
      }
    }
  }
}

.section-settle {
  position: relative;
  overflow: hidden;
  padding-top: 6.25rem;

  .container {
    padding: 35px 50px;
    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 25px;
      max-width: 50%;

      p {
        font-size: $font-size-18px;
        color: $color-secondary;
      }
    }
  }

  .transform-img {
    position: absolute;
    right: 0px;
    top: 60%;
    transform: translateY(-50%);
    width: 45%;
  }
}

.section-investments {
  padding: 2.5rem 0 8rem 0;

  .content {
    display: flex;
    flex-direction: column;
    gap: 25px;

    .items {
      display: flex;
      gap: 50px;

      .item {
        display: flex;
        flex-direction: column;
        flex-basis: calc(33.333% - calc(20px * 2 / 3));
        gap: 10px;

        img {
          max-width: 100px;
          transition: $transition;

          &:hover {
            transition: $transition;
            filter: $filter;
          }
        }

        .sub-title {
          font-family: $font-secondary;
          color: $color-primary;
          font-weight: 400;
          font-size: $font-size-32px;
        }

        .description {
          font-size: $font-size-18px;
          color: $color-secondary;
        }
      }
    }
  }
}

.section-mockup {
  padding: 5rem 0 8rem 0;

  .content {
    display: flex;
    flex-direction: column;
    gap: 40px;

    .section-title {
      text-align: center;
    }

    .img {
      width: 100%;
    }
  }
}

.section-comfort {
  padding: 7.5rem 0;
  color: $color-black;

  .left-right {
    gap: 20px;
    .left {
      display: flex;
      flex-direction: column;
      flex-basis: calc(50% - 20px);
      gap: 25px;
      .section-title {
        color: $color-white;

        .highlight {
          background-color: $color-primary;
        }
      }

      p {
        font-size: $font-size-18px;
        font-weight: 300;
      }
    }
    .right {
      display: flex;
      justify-content: flex-end;
      flex: 1;

      .items {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        width: 80%;

        .item {
          display: flex;
          align-items: center;
          padding: 3rem 0;
          gap: 25px;

          &:nth-child(2) {
            border-block: 0.5px solid $color-hover;
          }
        }
      }
    }
  }
}

.section-image {
  min-height: 595px;
  background-image: url("../../public/frame-1.webp");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: bottom center;
}

.section-location {
  padding: 7.5rem 0;

  .section-title {
    text-align: center;
    padding-bottom: 40px;
  }

  .left-right {
    .left,
    .right {
      display: flex;
      flex-direction: column;
      gap: 25px;
      flex-basis: calc(50% - 25px);
      font-weight: 400;
      font-size: $font-size-16px;
      color: $color-secondary;
    }
  }

  img {
    padding-top: 7.5rem;
    width: 100%;
  }
}

.section-schedule {
  padding: 5rem 0;
  color: $color-white;

  hr {
    margin: 4rem 0;
  }

  .left-right {
    gap: 20px;

    .left {
      display: flex;
      flex-direction: column;
      flex-basis: calc(35% - 50px);
      gap: 25px;
    }

    .right {
      display: flex;
      justify-content: flex-end;
      flex: 1;

      .items {
        display: flex;
        gap: 50px;
        width: 100%;

        hr {
          margin: 0;
          transform: translate(50%, 55%) rotate(90deg);
          height: 80px;
          z-index: -1;
        }

        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex-basis: 33.333%;
          gap: 25px;

          span {
            display: flex;
            flex-direction: column;
            text-align: center;
            color: $color-black;
          }
        }
      }
    }
  }
  .column {
    display: flex;
    flex-direction: column;

    .headers {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 32px;

      .tab {
        cursor: pointer;
        font-weight: bold;
        color: $color-black;
        text-align: center;
        font-weight: 500;

        &.active {
          color: $color-primary;
          font-weight: 700;
        }
      }

      hr {
        margin: 0;
        transform: rotate(90deg);
        height: 32px;
      }
    }

    .images {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      padding: 3rem 0;

      .image-set {
        display: none;

        img {
          max-width: 100%;
          height: auto;
          flex: 1;
          min-width: 200px;
        }

        &.active {
          display: flex;
          gap: 30px;
          width: 100%;
        }

        &.fade-in {
          opacity: 0;
          animation: fadeIn 0.5s forwards;
          pointer-events: none;
        }
      }
      
      @keyframes fadeIn {
        to {
          opacity: 1;
        }
      }
    }
  }
}

.section-visualization {
  padding: 5rem 0;

  .left-right {
    .left {
      display: flex;
      flex-direction: column;
      flex-basis: calc(40% - 20px);
      gap: 25px;
      justify-content: center;
    }

    .right {
      display: flex;
      justify-content: flex-end;
      flex: 1;

      .items {
        display: flex;
        gap: 40px;

        .item {
          display: flex;
          flex-direction: column;
          flex-basis: calc(50% - 15px);
          gap: 5px;

          img {
            padding-bottom: 1rem;
          }

          .sub-title {
            font-size: $font-size-20px;
            line-height: 1.641rem;
          }

          .description {
            font-size: $font-size-16px;
          }

          .link {
            display: flex;
            gap: 10px;
            align-items: center;
            font-size: $font-size-16px;
            font-weight: 700;
            color: $color-primary;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

.section-featured {
  .section-title {
    text-align: center;
  }
}

@media (max-width: $media-hd) {
}

@media (max-width: $media-laptop) {
}

@media (max-width: $media-tablet) {
  .homepage-content {
    .container {
      padding-top: 30px;

      .content {
        .title {
          font-size: $font-size-36px;
        }

        .sub-title {
          font-size: $font-size-16px;
        }
      }
    }
  }

  .section-settle {
    padding-top: 3rem;

    .container {
      padding: 0 15px;

      .content {
        max-width: 100%;
        min-height: unset;

        p {
          font-size: $font-size-16px;
        }
      }
    }

    .transform-img {
      position: unset;
      transform: unset;
      width: 100%;
    }
  }

  .section-mockup {
    padding: 5rem 0;
  }

  .section-investments {
    padding: 2.5rem 0 6rem 0;

    .content {
      .items {
        flex-wrap: wrap;
        gap: 25px;

        .item {
          flex-basis: 100%;

          .sub-title {
            font-size: $font-size-24px;
          }

          .description {
            font-size: $font-size-16px;
          }
        }
      }
    }
  }

  .section-comfort {
    padding: 5rem 0;

    .left-right {
      flex-direction: column;

      .left {
        display: flex;
        flex-direction: column;
        flex-basis: 100%;
        gap: 25px;

        p {
          font-size: $font-size-16px;
        }
      }

      .right {
        justify-content: flex-start;
        .items {
          width: 100%;
        }
      }
    }
  }

  .section-location {
    padding: 5rem 0;

    .section-title {
      padding-bottom: 20px;
    }

    .left-right {
      flex-direction: column;
      .left,
      .right {
        flex-basis: 100%;
      }
    }

    img {
      padding-top: 5rem;
    }
  }

  .section-schedule {
    padding: 3rem 0;

    hr {
      margin: 2rem 0;
    }

    .left-right {
      .left {
        flex-basis: 100%;

        .section-title {
          padding-bottom: 1rem;
          text-align: center;
        }
      }

      .right {
        justify-content: center;

        .items {
          flex-direction: column;
          gap: 25px;

          hr {
            margin: 0;
            transform: unset;
            height: unset;
          }

          .item {
            flex-basis: 100%;
          }
        }
      }
    }
    .column {
      .headers {
        justify-content: space-between;

        hr {
          height: 50px;
        }
      }

      .images {
        padding: 3rem 0;

        .image-set {
          flex-wrap: wrap;
          gap: 30px;
        }
      }
    }
  }

  .section-image {
    min-height: 320px;
    background-position: 30% 50%;
  }

  .section-visualization {
    padding: 3rem 0;

    .left-right {
      .left {
        flex-basis: 100%;
      }

      .right {
        justify-content: flex-start;
        .items {
          width: 100%;
          height: 100%;

          .item {
            flex-basis: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: $media-mobile) {
  .section-visualization {
    .left-right {
      .right {
        .items {
          flex-direction: column;
        }
      }
    }
  }
}

@media (max-width: $media-mobile-sm) {
}
