#tagsdiv-typ,
#tagsdiv-udogod<PERSON>nia,
#statusdiv {
  display: none;
}

#mieszkanie_details .inside {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
#mieszkanie_details .taxonomies {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
}
#mieszkanie_details .taxonomies ul {
  margin: 0;
}
#mieszkanie_details .taxonomies label {
  margin-left: 5px;
}
#mieszkanie_details .images {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  padding: 1rem 0;
}
#mieszkanie_details .images .button {
  width: fit-content;
  height: fit-content;
}
#mieszkanie_details .images #vertical_preview, #mieszkanie_details .images #angled_preview {
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 30px;
}

#wpbody-content .left-right {
  display: flex;
  gap: 20px;
}
#wpbody-content .left-right .left {
  flex-basis: calc(50% - 20px);
}
#wpbody-content .left-right .left .form-table td {
  padding: 0;
}
#wpbody-content .left-right .left .form-table .select2 {
  width: 100% !important;
  max-width: 100%;
}
#wpbody-content .left-right .right {
  flex: 1;
}

@media (max-width: 991.98px) {
  #wpbody-content .left-right {
    flex-direction: column;
  }
  #wpbody-content .left-right .left {
    flex-basis: 100%;
  }
}
@media (max-width: 767.98px) {
  .images #vertical_preview, .images #angled_preview {
    flex-direction: column;
  }
}