@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Libre+Bodoni:ital,wght@0,400..700;1,400..700&display=swap");
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}
html #rzuty {
  scroll-margin-top: 40px;
}

body,
ul,
li,
p,
h1,
h2,
h3,
h4,
h5,
h6,
a,
ol {
  margin: 0;
  padding: 0;
  text-decoration: none;
  list-style-type: none;
}

body {
  font-weight: 400;
  line-height: 1.744rem;
  font-family: "DM Sans";
  background-color: #FFFFFF;
  font-size: 1.125rem;
}

input,
textarea,
button,
select,
a {
  -webkit-tap-highlight-color: transparent !important;
}

ol {
  list-style-type: decimal;
}
ol li {
  list-style-type: decimal;
}
ol li ul li {
  list-style-type: disc;
}

.button {
  background: #0C387D;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  border: 1px solid #0C387D;
  border-radius: 4px;
  font-family: "DM Sans";
  font-size: 1rem;
  text-transform: uppercase;
  font-weight: 700;
  transition: 0.2s ease-in-out;
}
.button:hover, .button:focus, .button:active {
  background: #3063B4;
  border-color: #3063B4;
  color: #FFFFFF;
  cursor: pointer;
}
.button.reverse {
  background: #FFFFFF;
  color: #181715;
}
.button.reverse:hover, .button.reverse:focus, .button.reverse:active {
  background: #DCEAFF;
  transition: 0.2s ease-in-out;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
  line-height: 3.6rem;
  font-family: "Libre Bodoni";
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.8rem;
}

h3 {
  font-size: 1.6rem;
}

h4 {
  font-size: 1.4rem;
}

h5 {
  font-size: 1.2rem;
}

h6 {
  font-size: 1rem;
}

a {
  color: inherit;
}
a:hover, a:focus {
  color: #003B7B;
}

b {
  font-weight: bold;
}

strong {
  font-weight: 600;
}

img {
  max-width: 100%;
  width: auto;
  height: auto;
}

.container {
  margin: auto;
  max-width: 1440px;
  width: 100%;
  padding: 0 50px;
}

.container-fluid {
  margin: auto;
  max-width: 100%;
  width: 100%;
  padding: 0 50px;
}

.container-post {
  margin: auto;
  max-width: 1220px;
  width: 100%;
  padding: 0 50px;
}

.center {
  display: flex;
  justify-content: center;
}

.left-right {
  display: flex;
  gap: 25px;
  flex-wrap: nowrap;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.margin-0 {
  margin: 0 !important;
}

.margin-bottom-0 {
  margin-bottom: 0 !important;
}

.margin-top-0 {
  margin-top: 0 !important;
}

.margin-top-3 {
  margin-top: 3rem;
}

.margin-top-5 {
  margin-top: 5rem;
}

.padding-top-5 {
  padding-top: 5rem;
}

.desktop {
  display: block;
}

.mobile {
  display: none;
}

.section-title {
  font-size: 3rem;
  color: #181715;
  font-weight: 400;
}

.highlight {
  color: #FFFFFF;
  background-color: #003B7B;
  padding: 5px 10px;
}

#main-content ol, #main-content li {
  padding-inline-start: 40px;
}
#main-content .section-contact ol, #main-content .section-contact li {
  padding-inline-start: 40px;
}
#main-content.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh;
  gap: 30px;
}
#main-content.error .section-title {
  text-align: center;
}

body.page .section-cta, body.single-mieszkanie .section-cta {
  margin-top: 5rem;
  margin-bottom: 0;
}
body.home .section-cta {
  margin-bottom: 3rem;
}

.section-cta {
  background-image: url("../../public/cta-background.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.section-cta .container {
  display: flex;
  justify-content: center;
  min-height: 434px;
}
.section-cta .container .content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 25px;
}
.section-cta .container .content .section-title {
  font-size: 3.5rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 4.6rem;
}
.section-cta .container .content .button:hover, .section-cta .container .content .button:focus {
  color: #181715;
}

.homepage-content {
  min-height: 90vh;
  background-image: url("../../public/hallera_front.webp");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top;
}
.homepage-content .container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-top: 60px;
  padding-bottom: 30px;
}
.homepage-content .container .content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 25px;
}
.homepage-content .container .content .title {
  font-size: 3.5rem;
  line-height: 1.5em;
  text-align: center;
}
.homepage-content .container .content .sub-title {
  font-family: "DM Sans";
  font-size: 1.125rem;
  font-weight: 400;
}

.section-settle {
  position: relative;
  overflow: hidden;
  padding-top: 6.25rem;
}
.section-settle .container {
  padding: 35px 50px;
}
.section-settle .container .content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 25px;
  max-width: 50%;
}
.section-settle .container .content p {
  font-size: 1.125rem;
  color: #4D4C46;
}
.section-settle .transform-img {
  position: absolute;
  right: 0px;
  top: 60%;
  transform: translateY(-50%);
  width: 45%;
}

.section-investments {
  padding: 2.5rem 0 8rem 0;
}
.section-investments .content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}
.section-investments .content .items {
  display: flex;
  gap: 50px;
}
.section-investments .content .items .item {
  display: flex;
  flex-direction: column;
  flex-basis: calc(33.333% - 13.3333333333px);
  gap: 10px;
}
.section-investments .content .items .item img {
  max-width: 100px;
  transition: 0.2s ease-in-out;
}
.section-investments .content .items .item img:hover {
  transition: 0.2s ease-in-out;
  filter: brightness(0) saturate(100%) invert(14%) sepia(99%) saturate(2043%) hue-rotate(198deg) brightness(94%) contrast(102%);
}
.section-investments .content .items .item .sub-title {
  font-family: "DM Sans";
  color: #003B7B;
  font-weight: 400;
  font-size: 2rem;
}
.section-investments .content .items .item .description {
  font-size: 1.125rem;
  color: #4D4C46;
}

.section-mockup {
  padding: 5rem 0 8rem 0;
}
.section-mockup .content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.section-mockup .content .section-title {
  text-align: center;
}
.section-mockup .content .img {
  width: 100%;
}

.section-comfort {
  padding: 7.5rem 0;
  color: #181715;
}
.section-comfort .left-right {
  gap: 20px;
}
.section-comfort .left-right .left {
  display: flex;
  flex-direction: column;
  flex-basis: calc(50% - 20px);
  gap: 25px;
}
.section-comfort .left-right .left .section-title {
  color: #FFFFFF;
}
.section-comfort .left-right .left .section-title .highlight {
  background-color: #003B7B;
}
.section-comfort .left-right .left p {
  font-size: 1.125rem;
  font-weight: 300;
}
.section-comfort .left-right .right {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}
.section-comfort .left-right .right .items {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  width: 80%;
}
.section-comfort .left-right .right .items .item {
  display: flex;
  align-items: center;
  padding: 3rem 0;
  gap: 25px;
}
.section-comfort .left-right .right .items .item:nth-child(2) {
  border-block: 0.5px solid #DCEAFF;
}

.section-image {
  min-height: 595px;
  background-image: url("../../public/frame-1.webp");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: bottom center;
}

.section-location {
  padding: 7.5rem 0;
}
.section-location .section-title {
  text-align: center;
  padding-bottom: 40px;
}
.section-location .left-right .left,
.section-location .left-right .right {
  display: flex;
  flex-direction: column;
  gap: 25px;
  flex-basis: calc(50% - 25px);
  font-weight: 400;
  font-size: 1rem;
  color: #4D4C46;
}
.section-location img {
  padding-top: 7.5rem;
  width: 100%;
}

.section-schedule {
  padding: 5rem 0;
  color: #FFFFFF;
}
.section-schedule hr {
  margin: 4rem 0;
}
.section-schedule .left-right {
  gap: 20px;
}
.section-schedule .left-right .left {
  display: flex;
  flex-direction: column;
  flex-basis: calc(35% - 50px);
  gap: 25px;
}
.section-schedule .left-right .right {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}
.section-schedule .left-right .right .items {
  display: flex;
  gap: 50px;
  width: 100%;
}
.section-schedule .left-right .right .items hr {
  margin: 0;
  transform: translate(50%, 55%) rotate(90deg);
  height: 80px;
  z-index: -1;
}
.section-schedule .left-right .right .items .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-basis: 33.333%;
  gap: 25px;
}
.section-schedule .left-right .right .items .item span {
  display: flex;
  flex-direction: column;
  text-align: center;
  color: #181715;
}
.section-schedule .column {
  display: flex;
  flex-direction: column;
}
.section-schedule .column .headers {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
}
.section-schedule .column .headers .tab {
  cursor: pointer;
  font-weight: bold;
  color: #181715;
  text-align: center;
  font-weight: 500;
}
.section-schedule .column .headers .tab.active {
  color: #003B7B;
  font-weight: 700;
}
.section-schedule .column .headers hr {
  margin: 0;
  transform: rotate(90deg);
  height: 32px;
}
.section-schedule .column .images {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  padding: 3rem 0;
}
.section-schedule .column .images .image-set {
  display: none;
}
.section-schedule .column .images .image-set img {
  max-width: 100%;
  height: auto;
  flex: 1;
  min-width: 200px;
}
.section-schedule .column .images .image-set.active {
  display: flex;
  gap: 30px;
  width: 100%;
}
.section-schedule .column .images .image-set.fade-in {
  opacity: 0;
  animation: fadeIn 0.5s forwards;
  pointer-events: none;
}
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.section-visualization {
  padding: 5rem 0;
}
.section-visualization .left-right .left {
  display: flex;
  flex-direction: column;
  flex-basis: calc(40% - 20px);
  gap: 25px;
  justify-content: center;
}
.section-visualization .left-right .right {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}
.section-visualization .left-right .right .items {
  display: flex;
  gap: 40px;
}
.section-visualization .left-right .right .items .item {
  display: flex;
  flex-direction: column;
  flex-basis: calc(50% - 15px);
  gap: 5px;
}
.section-visualization .left-right .right .items .item img {
  padding-bottom: 1rem;
}
.section-visualization .left-right .right .items .item .sub-title {
  font-size: 1.25rem;
  line-height: 1.641rem;
}
.section-visualization .left-right .right .items .item .description {
  font-size: 1rem;
}
.section-visualization .left-right .right .items .item .link {
  display: flex;
  gap: 10px;
  align-items: center;
  font-size: 1rem;
  font-weight: 700;
  color: #003B7B;
}
.section-visualization .left-right .right .items .item .link:hover {
  text-decoration: underline;
}

.section-featured .section-title {
  text-align: center;
}

@media (max-width: 991.98px) {
  .homepage-content .container {
    padding-top: 30px;
  }
  .homepage-content .container .content .title {
    font-size: 2.25rem;
  }
  .homepage-content .container .content .sub-title {
    font-size: 1rem;
  }
  .section-settle {
    padding-top: 3rem;
  }
  .section-settle .container {
    padding: 0 15px;
  }
  .section-settle .container .content {
    max-width: 100%;
    min-height: unset;
  }
  .section-settle .container .content p {
    font-size: 1rem;
  }
  .section-settle .transform-img {
    position: unset;
    transform: unset;
    width: 100%;
  }
  .section-mockup {
    padding: 5rem 0;
  }
  .section-investments {
    padding: 2.5rem 0 6rem 0;
  }
  .section-investments .content .items {
    flex-wrap: wrap;
    gap: 25px;
  }
  .section-investments .content .items .item {
    flex-basis: 100%;
  }
  .section-investments .content .items .item .sub-title {
    font-size: 1.5rem;
  }
  .section-investments .content .items .item .description {
    font-size: 1rem;
  }
  .section-comfort {
    padding: 5rem 0;
  }
  .section-comfort .left-right {
    flex-direction: column;
  }
  .section-comfort .left-right .left {
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
    gap: 25px;
  }
  .section-comfort .left-right .left p {
    font-size: 1rem;
  }
  .section-comfort .left-right .right {
    justify-content: flex-start;
  }
  .section-comfort .left-right .right .items {
    width: 100%;
  }
  .section-location {
    padding: 5rem 0;
  }
  .section-location .section-title {
    padding-bottom: 20px;
  }
  .section-location .left-right {
    flex-direction: column;
  }
  .section-location .left-right .left,
  .section-location .left-right .right {
    flex-basis: 100%;
  }
  .section-location img {
    padding-top: 5rem;
  }
  .section-schedule {
    padding: 3rem 0;
  }
  .section-schedule hr {
    margin: 2rem 0;
  }
  .section-schedule .left-right .left {
    flex-basis: 100%;
  }
  .section-schedule .left-right .left .section-title {
    padding-bottom: 1rem;
    text-align: center;
  }
  .section-schedule .left-right .right {
    justify-content: center;
  }
  .section-schedule .left-right .right .items {
    flex-direction: column;
    gap: 25px;
  }
  .section-schedule .left-right .right .items hr {
    margin: 0;
    transform: unset;
    height: unset;
  }
  .section-schedule .left-right .right .items .item {
    flex-basis: 100%;
  }
  .section-schedule .column .headers {
    justify-content: space-between;
  }
  .section-schedule .column .headers hr {
    height: 50px;
  }
  .section-schedule .column .images {
    padding: 3rem 0;
  }
  .section-schedule .column .images .image-set {
    flex-wrap: wrap;
    gap: 30px;
  }
  .section-image {
    min-height: 320px;
    background-position: 30% 50%;
  }
  .section-visualization {
    padding: 3rem 0;
  }
  .section-visualization .left-right .left {
    flex-basis: 100%;
  }
  .section-visualization .left-right .right {
    justify-content: flex-start;
  }
  .section-visualization .left-right .right .items {
    width: 100%;
    height: 100%;
  }
  .section-visualization .left-right .right .items .item {
    flex-basis: 100%;
  }
}
@media (max-width: 767.98px) {
  .section-visualization .left-right .right .items {
    flex-direction: column;
  }
}
.menu-container {
  background-color: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 999;
  width: 100%;
  height: fit-content;
  transform-origin: top;
}
.menu-container.scrolled .menu {
  padding: 15px 0;
  box-shadow: 0 0 5px #FFFFFF;
}
.menu-container .menu {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
  padding: 25px 0;
  transition: padding 0.3s ease-in-out, box-shadow 0.3s ease;
  position: relative;
}
.menu-container .menu .logo-container {
  width: auto;
  display: inline-flex;
  align-items: center;
  transform: translateX(-60%);
}
.menu-container .menu .logo-container img {
  display: inline-block;
  max-height: 130px;
  max-width: 130px;
}
.menu-container .menu .right {
  display: flex;
  align-items: center;
  gap: 25px;
}
.menu-container .menu .right a.active {
  box-shadow: 0px 0px 19.9px rgba(0, 59, 123, 0.6);
}
.menu-container .menu .main-menu {
  display: flex;
  gap: 15px;
}
.menu-container .menu .main-menu li {
  align-items: center;
  font-weight: 500;
  font-size: 1rem;
}
.menu-container .menu .main-menu li:first-of-type a {
  padding-left: 0;
}
.menu-container .menu .main-menu li.current_page_item {
  color: #003B7B;
  font-weight: 700;
}
.menu-container .menu .main-menu li a {
  padding: 5px 15px;
  display: inline-flex;
  flex-direction: row;
  position: relative;
}
.menu-container .menu .main-menu li a .dropdown-icon {
  display: none;
}
.menu-container .menu .main-menu li .sub-menu {
  transition: 0.2s ease-in-out;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  background-color: #003B7B;
  width: 100%;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  color: #FFFFFF;
  transition-delay: 0.1s;
  padding: 25px 0;
  z-index: 999;
}
.menu-container .menu .main-menu li .sub-menu li {
  float: left;
  width: calc(33.333% - 25px);
  margin-left: 25px;
  position: relative;
}
.menu-container .menu .main-menu li .sub-menu li a {
  display: inline-flex;
  justify-content: flex-start;
  min-width: 260px;
  width: auto;
  max-width: 100%;
  padding: 15px;
}
.menu-container .menu .main-menu li .sub-menu li a:hover, .menu-container .menu .main-menu li .sub-menu li a:focus {
  background-color: #055180;
  color: #FFFFFF;
}
.menu-container .menu .main-menu li .sub-menu li:after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 15px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.15);
  width: 100%;
  max-width: 230px;
}
.menu-container .menu .main-menu li .sub-menu:before, .menu-container .menu .main-menu li .sub-menu:after {
  content: "";
  position: absolute;
  top: 25px;
  height: calc(100% - 50px);
  width: 1px;
  background-color: #669dc0;
  left: calc(33.333% + 12.5px);
}
.menu-container .menu .main-menu li .sub-menu:after {
  right: calc(33.333% - 12.5px);
  left: auto;
}
.menu-container .menu .main-menu li.menu-item-has-children:before {
  height: 100px;
  content: "";
  width: 100%;
}
.menu-container .menu .main-menu li.menu-item-has-children > a .dropdown-icon {
  display: flex;
}
.menu-container .menu .main-menu li.menu-item-has-children > a .dropdown-icon svg {
  width: 12px;
  height: 8px;
}
.menu-container .menu .main-menu li.menu-item-has-children > a:before {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: block;
  content: "";
  background-color: #003B7B;
  height: 10px;
  transform: translateY(60px);
  transition: 0.2s ease-in-out;
  opacity: 0;
}
.menu-container .menu .main-menu li.menu-item-has-children > a:after {
  position: absolute;
  bottom: 0;
  top: 0;
  width: 100%;
  display: block;
  content: "";
  height: 77px;
}
.menu-container .menu .main-menu li.menu-item-has-children > a:hover + .sub-menu, .menu-container .menu .main-menu li.menu-item-has-children > a:focus + .sub-menu {
  opacity: 1;
  visibility: visible;
  transition-delay: 0s;
}
.menu-container .menu .main-menu li.menu-item-has-children:hover > a:before, .menu-container .menu .main-menu li.menu-item-has-children:focus > a:before, .menu-container .menu .main-menu li.menu-item-has-children:focus-within > a:before {
  opacity: 1;
  transform: translateY(44px);
  transition-delay: 0.1s;
}
.menu-container .menu .main-menu li.menu-item-has-children:hover .sub-menu, .menu-container .menu .main-menu li.menu-item-has-children:focus .sub-menu, .menu-container .menu .main-menu li.menu-item-has-children:focus-within .sub-menu {
  opacity: 1;
  visibility: visible;
  transition-delay: 0s;
}

@media (max-width: 991.98px) {
  .menu-container {
    position: fixed;
    top: 90px;
    width: 100%;
    height: fit-content;
    transition: height 0.3s ease;
    padding-bottom: 1rem;
    transform: translateX(110%);
  }
  .menu-container .menu {
    display: block;
  }
  .menu-container .menu .logo-container {
    display: none;
  }
  .menu-container .menu .main-menu {
    display: block;
  }
  .menu-container .menu .main-menu li {
    margin-bottom: 50px;
  }
  .menu-container .menu .main-menu li a {
    padding: 0;
  }
  .menu-container .menu .main-menu li .sub-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    color: #003B7B;
    transform: none;
    background-color: #FFFFFF;
    transition: 0s;
    display: none;
  }
  .menu-container .menu .main-menu li .sub-menu li {
    float: none;
    width: 100%;
  }
  .menu-container .menu .main-menu li .sub-menu li a {
    min-width: auto;
  }
  .menu-container .menu .main-menu li .sub-menu li a:after {
    content: none;
  }
  .menu-container .menu .main-menu li .sub-menu:before, .menu-container .menu .main-menu li .sub-menu:after {
    content: none;
  }
  .menu-container .menu .main-menu li.menu-item-has-children > a:before, .menu-container .menu .main-menu li.menu-item-has-children > a:after {
    content: none;
  }
  .menu-container .menu .main-menu li.menu-item-has-children > a .dropdown-icon {
    display: none;
  }
  .menu-container .menu .right .button {
    width: 100%;
  }
  .menu-container.menu-opened {
    z-index: 999;
    transform: translateX(0);
    border-top: 1px solid #897F7B;
  }
  .menu-container.menu-opened .header-navigation-item {
    display: none;
  }
  .menu-container.menu-opened .clipboard {
    display: none;
  }
}
.section-contact {
  padding: 2rem 0 5rem 0;
}
.section-contact .left-right .left {
  padding: 3rem 0;
  flex-basis: calc(50% - 25px);
}
.section-contact .left-right .left .menus {
  display: flex;
  flex-wrap: wrap;
  color: #4d4c46;
  font-family: "DM Sans";
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  width: 100%;
  gap: 15px;
}
.section-contact .left-right .left .menus .footer-menu .footer-menu-1 {
  padding: 3rem 0;
}
.section-contact .left-right .left .menus .flex {
  display: flex;
  flex-direction: column;
  gap: 35px;
  max-width: 75%;
}
.section-contact .left-right .left .menus .flex .footer-menu {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}
.section-contact .left-right .left .menus .flex .footer-menu img {
  max-width: 60px;
}
.section-contact .left-right .left .menus .flex .footer-menu .content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.section-contact .left-right .left .menus .flex .footer-menu .content .footer-menu-2, .section-contact .left-right .left .menus .flex .footer-menu .content .footer-menu-3, .section-contact .left-right .left .menus .flex .footer-menu .content .footer-menu-4 {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.section-contact .left-right .left .menus .menu-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #181715;
}
.section-contact .left-right .left .menus .footer-menu {
  word-break: break-word;
  width: 100%;
}
.section-contact .left-right .right {
  flex: 1;
  background-color: #003B7B;
  padding: 60px 60px 30px 60px;
}

.section-map .left-right .left {
  flex-basis: calc(45% - 25px);
  align-content: center;
}
.section-map .left-right .right {
  flex: 1;
}
.section-map .left-right .right iframe {
  width: 100%;
}

.wpcf7 form.invalid .wpcf7-response-output {
  border-color: rgb(220, 50, 50);
}

.wpcf7-response-output {
  font-size: 1rem;
  color: #FFFFFF;
}
.wpcf7 textarea,
.wpcf7 input,
.wpcf7 select {
  font-family: "DM Sans";
  font-size: 1rem;
  border-radius: 5px;
}
.wpcf7-two-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.wpcf7-column {
  width: calc(50% - 10px);
}
.wpcf7-column input {
  width: 100%;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.wpcf7-full-width {
  width: 100%;
}
.wpcf7-full-width a {
  color: #FFFFFF;
}
.wpcf7-full-width a:hover, .wpcf7-full-width a:focus {
  text-decoration: underline;
}
.wpcf7-full-width p {
  font-size: 0.875rem;
  color: #FFFFFF;
}
.wpcf7-full-width .wpcf7-form-control-wrap .wpcf7-list-item {
  display: flex;
  gap: 10px;
  margin: 0;
}
.wpcf7-full-width .wpcf7-form-control-wrap .wpcf7-list-item span {
  font-size: 0.875rem;
  font-weight: 300;
  color: #FFFFFF;
}
.wpcf7-full-width textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  height: 120px;
  resize: none;
  transition: height 0.5s ease;
}
.wpcf7-full-width textarea:focus {
  height: 150px;
  transition: height 0.5s ease;
}
.wpcf7-full-width input[type=submit] {
  font-weight: 700;
  width: 100%;
  background-color: #DCEAFF;
  color: #181715;
  text-transform: uppercase;
  padding: 12px;
  border: none;
  cursor: pointer;
  transition: 0.2s ease-in-out;
}
.wpcf7-full-width input[type=submit]:hover {
  background-color: #3063B4;
  transition: 0.2s ease-in-out;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox] {
  position: absolute;
  opacity: 0;
  width: 20px;
  height: 20px;
  top: 0;
  left: 0;
  z-index: 999;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox] + span {
  border-radius: 0;
  cursor: pointer;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox] + span::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border: 1px solid #dfd8bb;
  border-radius: 5px;
  background: transparent;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox] + span::after {
  content: "🗸";
  position: absolute;
  top: 0;
  left: 2px;
  width: 20px;
  height: 20px;
  font-size: 22px;
  text-align: center;
  line-height: 28px;
  color: #181715;
  visibility: hidden;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox]:checked + span::before {
  background: #FFFFFF;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox]:checked + span::after {
  visibility: visible;
}
.wpcf7 .wpcf7-checkbox input[type=checkbox]:not(:checked) + span::after {
  visibility: hidden;
}
.wpcf7 .wpcf7-list-item-label {
  margin-left: 40px;
  display: inline-block;
}

@media (max-width: 991.98px) {
  .section-contact {
    padding-top: 0;
  }
  .section-contact .left-right .left {
    flex-basis: 100%;
    padding: 0 0 1rem 0;
  }
  .section-contact .left-right .left .section-title {
    text-align: center;
  }
  .section-contact .left-right .left .menus .flex {
    max-width: 100%;
  }
  .section-contact .left-right .right {
    padding: 25px;
  }
  .section-map .left-right {
    flex-direction: column;
    gap: 50px;
  }
  .section-map .left-right .section-title {
    text-align: center;
  }
  .section-map .left-right .left {
    flex-basis: 100%;
  }
  .wpcf7-column {
    width: 100%;
  }
  .wpcf7 .wpcf7-list-item-label {
    margin-left: 30px;
  }
}
footer {
  margin-top: 5rem;
}
body:not(.page-template-template-kontakt) footer {
  margin: 0;
}
footer .footer-container {
  background-color: #003B7B;
  color: #FFFFFF;
  padding: 5rem 0;
}
footer .footer-container .container {
  display: flex;
  flex-direction: column;
  gap: 50px;
}
footer .footer-container .container a {
  text-decoration: none;
  color: #FFFFFF;
}
footer .footer-container .container a:hover, footer .footer-container .container a:focus {
  text-decoration: underline;
}
footer .footer-container .container .left-right {
  gap: 35px;
}
footer .footer-container .container .left-right .info {
  align-items: center;
  width: 100%;
}
footer .footer-container .container .left-right .info.left-right {
  min-height: 210px;
  max-width: 400px;
}
footer .footer-container .container .left-right .info.left-right .left {
  display: flex;
  flex-direction: column;
  gap: 25px;
  height: 100%;
}
footer .footer-container .container .left-right .info.left-right .left a {
  display: flex;
}
footer .footer-container .container .left-right .info.left-right .left .bottom {
  display: flex;
  gap: 25px;
  align-items: center;
}
footer .footer-container .container .left-right .info.left-right .left .bottom .social-links {
  transition: 0.2s ease-in-out;
}
footer .footer-container .container .left-right .info.left-right .left .bottom .social-links:hover {
  transform: scale(1.1);
  transition: 0.2s ease-in-out;
}
footer .footer-container .container .left-right .info.left-right .left .bottom .social-links img {
  max-width: 30px;
}
footer .footer-container .container .left-right .menus {
  display: flex;
  flex-wrap: wrap;
  color: #FFFFFF;
  font-family: "DM Sans";
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  width: 100%;
  gap: 15px;
}
footer .footer-container .container .left-right .menus .flex {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
footer .footer-container .container .left-right .menus .flex .footer-menu {
  display: flex;
  flex-basis: calc(30% - 10px);
  justify-content: center;
  align-items: flex-start;
  gap: 20px;
}
footer .footer-container .container .left-right .menus .flex .footer-menu:first-of-type {
  flex-basis: calc(40% - 10px);
}
footer .footer-container .container .left-right .menus .flex .footer-menu img {
  max-width: 45px;
}
footer .footer-container .container .left-right .menus .flex .footer-menu .content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
footer .footer-container .container .left-right .menus .flex .footer-menu .content .footer-menu-2, footer .footer-container .container .left-right .menus .flex .footer-menu .content .footer-menu-3, footer .footer-container .container .left-right .menus .flex .footer-menu .content .footer-menu-4 {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
footer .footer-container .container .left-right .menus .menu-title {
  font-size: 1.25rem;
  font-weight: 500;
}
footer .footer-container .container .left-right .menus .footer-menu {
  word-break: break-word;
  width: 100%;
}
footer .footer-container .container .left-right li {
  padding-bottom: 16px;
}
footer .footer-bottom .footer-menu {
  display: flex;
  justify-content: space-between;
}
footer .footer-bottom .footer-menu .footer-menu-5 {
  display: flex;
  gap: 25px;
}

@media (max-width: 1199.98px) {
  footer .footer-container .container .left-right {
    flex-wrap: wrap;
    gap: 0;
  }
  footer .footer-container .container .left-right .info.left-right {
    max-width: 100%;
  }
  footer .footer-container .container .left-right .menus .flex .footer-menu {
    flex-basis: calc(33.3333333333% - 10px);
  }
  footer .footer-container .container .left-right .menus .flex .footer-menu:first-of-type {
    flex-basis: calc(33.3333333333% - 10px);
  }
}
@media (max-width: 767.98px) {
  footer .footer-container .container .left-right {
    gap: 15px;
  }
  footer .footer-container .container .left-right .menus {
    gap: 35px;
  }
  footer .footer-container .container .left-right .menus .flex {
    flex-direction: column;
    gap: 25px;
  }
  footer .footer-container .container .left-right .menus .flex .footer-menu {
    flex-basis: 100%;
    justify-content: flex-start;
  }
  footer .footer-container .container .left-right .menus .flex .footer-menu:first-of-type {
    flex-basis: 100%;
  }
  footer .footer-container .container .footer-bottom .footer-menu {
    flex-direction: column-reverse;
    gap: 35px;
  }
  footer .footer-container .container .footer-bottom .footer-menu .footer-menu-5 {
    justify-content: space-between;
    flex-wrap: wrap;
  }
}
#main-content.single {
  margin-top: 30px;
}

.mieszkania-template {
  padding-bottom: 5rem;
}
.mieszkania-template .section-title {
  text-align: center;
}
.mieszkania-template .pricing {
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
}
.mieszkania-template .pricing .block {
  display: flex;
}
.mieszkania-template .pricing .block:not(:first-child)::before {
  content: "•";
  display: block;
  text-align: center;
  margin: 0 1rem;
  font-size: 2rem;
  color: #181715;
}
.mieszkania-template .pricing .block .info {
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  list-style-type: none;
}
.mieszkania-template .pricing .block .info .price {
  font-weight: bold;
  white-space: pre;
}
.mieszkania-template .left-right {
  padding-top: 5rem;
}
.mieszkania-template .left-right .left {
  display: flex;
  flex-direction: column;
  flex-basis: calc(25% - 10px);
  gap: 15px;
}
.mieszkania-template .left-right .left .title {
  font-weight: 400;
}
.mieszkania-template .left-right .left .floor-selection {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.mieszkania-template .left-right .left .floor-selection button .reverse .active {
  background: #0C387D;
}
.mieszkania-template .left-right .left .floor-selection a {
  text-align: center;
}
.mieszkania-template .left-right .right {
  flex: 1 1 0;
}
.mieszkania-template .left-right .right .caption {
  display: flex;
  justify-content: flex-end;
}
.mieszkania-template .left-right .right .caption .legend {
  display: flex;
  gap: 30px;
  align-items: center;
}
.mieszkania-template .left-right .right .caption .legend .legend-title {
  font-weight: 700;
  font-size: 1rem;
}
.mieszkania-template .left-right .right .caption .legend .legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.mieszkania-template .left-right .right .caption .legend .legend-item .legend-text {
  font-size: 1rem;
}
.mieszkania-template .left-right .right .caption .legend .legend-item .color-box {
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
.mieszkania-template .left-right .right .caption .legend .legend-item.dostepne .color-box {
  background-color: #38894a;
}
.mieszkania-template .left-right .right .caption .legend .legend-item.rezerwacja .color-box {
  background-color: #d79729;
}
.mieszkania-template .left-right .right .caption .legend .legend-item.sprzedane .color-box {
  background-color: #c53521;
}
.mieszkania-template .left-right .right .floor-plan {
  position: relative;
}
.mieszkania-template .left-right .right .floor-plan .compass {
  position: absolute;
  bottom: 0;
  left: 0;
}
.mieszkania-template .left-right .right .floor-plan .hotspots-image-container,
.mieszkania-template .left-right .right .floor-plan .leaflet-container {
  background: transparent !important;
}
.mieszkania-template .left-right .right .floor-plan .hotspots-container .hotspots-image-container .hotspots-image {
  position: relative;
  max-width: 700px !important;
  object-fit: contain;
  aspect-ratio: 1/1;
  width: 100%;
}

@media (max-width: 991.98px) {
  .mieszkania-template {
    padding-bottom: 3rem;
  }
  .mieszkania-template .pricing {
    flex-direction: column;
    align-items: center;
  }
  .mieszkania-template .pricing .block {
    display: block;
  }
  .mieszkania-template .pricing .block .info {
    flex-basis: 100%;
    text-align: center;
    list-style-type: disc;
  }
  .mieszkania-template .left-right {
    padding-top: 3rem;
  }
  .mieszkania-template .left-right .left {
    flex-basis: 100%;
  }
  .mieszkania-template .left-right .left .floor-selection {
    flex-direction: row;
    flex-wrap: wrap;
  }
  .mieszkania-template .left-right .left .floor-selection .button {
    flex-basis: calc(50% - 10px);
  }
  .mieszkania-template .left-right .left .floor-selection .button:last-of-type {
    flex-basis: 100%;
  }
  .mieszkania-template .left-right .right {
    overflow-x: hidden;
  }
  .mieszkania-template .left-right .right .caption {
    justify-content: center;
  }
  .mieszkania-template .left-right .right .caption .legend {
    flex-wrap: wrap;
    gap: 5px;
  }
  .mieszkania-template .left-right .right .caption .legend .legend-title {
    margin-right: 10px;
  }
  .mieszkania-template .left-right .right .floor-plan .compass {
    right: 0;
    left: unset;
    max-width: 60px;
  }
}
#main-content.archive {
  margin-top: 3rem;
}

.mobile-container {
  position: fixed;
  z-index: 1200;
  background-color: #FFFFFF;
  display: none;
  top: 0;
  width: 100%;
  transition: 0.2s ease-in-out;
  height: 90px;
  justify-content: space-between;
}
.mobile-container a.logo-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 15px;
  flex-grow: 1;
}
.mobile-container a.logo-container img {
  max-height: 100%;
  width: auto;
  max-width: 70%;
}
.mobile-container .right {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}
.mobile-container .hamburger-toggle-container {
  height: 100%;
  width: auto;
  transition: 0.2s ease-in-out;
  text-decoration: none;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle {
  user-select: none;
  cursor: pointer;
  height: 25px;
  width: 25px;
  margin: 0 15px;
  display: flex;
  align-items: center;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle-bar, .mobile-container .hamburger-toggle-container .hamburger-toggle-bar:before, .mobile-container .hamburger-toggle-container .hamburger-toggle-bar:after {
  position: absolute;
  transition: 0.2s ease-in-out;
  background: #181715;
  content: "";
  height: 3px;
  width: 25px;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle-bar {
  margin-top: 0px;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle-bar:before {
  margin-top: -7px;
  left: 0;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle-bar:after {
  margin-top: 7px;
  left: 0;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle.expanded .hamburger-toggle-bar {
  background-color: transparent;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle.expanded .hamburger-toggle-bar:before, .mobile-container .hamburger-toggle-container .hamburger-toggle.expanded .hamburger-toggle-bar:after {
  transform: rotate(-45deg);
  margin-top: 0;
}
.mobile-container .hamburger-toggle-container .hamburger-toggle.expanded .hamburger-toggle-bar:before {
  transform: rotate(45deg);
  margin-top: 0;
}

@media (max-width: 991.98px) {
  .mobile-container {
    display: flex;
  }
}
.mieszkanie-single .left-right .left .main-image .placeholder-text {
  color: #181715;
  font-size: 1rem;
}
.mieszkanie-single .left-right .left .gallery .thumbnail,
.mieszkanie-single .left-right .left .gallery .walkaround {
  border: 1px solid #0C387D;
}
.mieszkanie-single .left-right .left .gallery .thumbnail .title,
.mieszkanie-single .left-right .left .gallery .walkaround .title {
  color: #181715;
}
.mieszkanie-single .left-right .left .gallery .thumbnail {
  transition: 0.2s ease-in-out;
}
.mieszkanie-single .left-right .left .gallery .thumbnail.active {
  transition: 0.2s ease-in-out;
  box-shadow: 0px 0px 19.9px rgba(0, 59, 123, 0.6);
}
.mieszkanie-single .left-right .right .info .status {
  font-weight: 700;
}
.mieszkanie-single .left-right .right .info .link-pdf {
  background: #003B7B;
  color: #FFFFFF;
  transition: 0.2s ease-in-out;
}
.mieszkanie-single .left-right .right .info .link-pdf:hover {
  background: #3063B4;
  transition: 0.2s ease-in-out;
}
.mieszkanie-single .left-right .right .info .link-pdf.reverse {
  background: #FFFFFF;
  border: 1px solid #0C387D;
  color: #181715;
}
.mieszkanie-single .left-right .right .info .link-pdf.reverse:hover {
  background: #DCEAFF;
  transition: 0.2s ease-in-out;
}

.mieszkaniaSwiper .mieszkanie {
  transition: 0.2s ease-in-out;
}
.mieszkaniaSwiper .mieszkanie:hover, .mieszkaniaSwiper .mieszkanie:focus {
  transition: 0.2s ease-in-out;
  background: #DCEAFF;
}

.mieszkanie .property-link .title {
  font-family: "DM Sans";
  color: #003B7B;
}
.mieszkanie .property-link .bottom .price .price-value {
  color: #181715;
}

.mieszkania-list button {
  background: #0C387D;
}

#mieszkania-clipboard .dd_clipboard-title .column h1,
#mieszkania-clipboard .dd_clipboard-title .column #dd_clipboard-count {
  font-size: 3rem;
  font-family: "Libre Bodoni";
}
#mieszkania-clipboard .dd_clipboard-title .column h1 span,
#mieszkania-clipboard .dd_clipboard-title .column #dd_clipboard-count span {
  color: #003B7B;
}
#mieszkania-clipboard .dd_clipboard-title button {
  font-family: "DM Sans";
  color: rgba(41, 41, 41, 0.8);
  font-size: 1.125rem;
}
#mieszkania-clipboard .dd_clipboard-title button:hover {
  font-weight: 500;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie {
  border: 1px solid #0C387D;
  transition: 0.2s ease-in-out;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie:hover, #mieszkania-clipboard #dd_clipboard-list .mieszkanie:focus {
  transition: 0.2s ease-in-out;
  box-shadow: 0px 0px 19.9px rgba(0, 59, 123, 0.6);
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie button {
  background: none;
  width: fit-content;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie button svg:hover > rect {
  fill: #3063B4;
  border-color: #3063B4;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie button svg:hover path {
  stroke: #FFFFFF;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie.fade-out {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.mieszkaniaSwiper button,
.mieszkanie-single button {
  background: #003B7B;
}
.mieszkaniaSwiper button:hover, .mieszkaniaSwiper button:focus,
.mieszkanie-single button:hover,
.mieszkanie-single button:focus {
  background: #3063B4;
}

.leaflet-rrose-content-wrapper {
  border: 1px solid #003B7B !important;
  border-radius: 0 !important;
}
.leaflet-rrose-content-wrapper .leaflet-rrose-content .mieszkanie-tooltip {
  font-family: "DM Sans";
  font-weight: 400;
}
.leaflet-rrose-content-wrapper .leaflet-rrose-content .mieszkanie-tooltip .row:first-of-type {
  font-weight: 700;
}
.leaflet-rrose-content-wrapper .leaflet-rrose-content .mieszkanie-tooltip .row:first-of-type.sprzedane {
  color: #c53521;
}
.leaflet-rrose-content-wrapper .leaflet-rrose-content .mieszkanie-tooltip .row:first-of-type.dostepne {
  color: #38894a;
}
.leaflet-rrose-content-wrapper .leaflet-rrose-content .mieszkanie-tooltip .row:first-of-type.rezerwacja {
  color: #d79729;
}
.leaflet-rrose-content-wrapper .leaflet-rrose-content .mieszkanie-tooltip .button {
  color: #FFFFFF;
}

.swiper-button-next:hover, .swiper-button-next:focus, .swiper-button-prev:hover, .swiper-button-prev:focus {
  border-radius: 50% !important;
  background: #DCEAFF !important;
}

.dd_add-to-clipboard svg path {
  stroke: #FFFFFF;
}

.dd_clipboard-button {
  border: 1px solid #003B7B;
  background: #003B7B;
}
.dd_clipboard-button:hover, .dd_clipboard-button:focus, .dd_clipboard-button:active {
  border-color: #3063B4;
  background: #3063B4;
}
.dd_clipboard-button .clipboard-status {
  color: #FFFFFF;
}

@media (max-width: 767.98px) {
  .mieszkanie-single .left-right .left .main-image .placeholder-text {
    font-size: 12px;
    line-height: 1.5;
  }
}
@media (max-width: 991.98px) {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 2.5rem;
  }
  .desktop {
    display: none;
  }
  .mobile {
    display: block;
  }
  .left-right {
    flex-wrap: wrap;
  }
  body {
    padding-top: 90px;
  }
  .section-title {
    font-size: 2rem;
  }
  .button.reverse {
    text-align: center;
  }
  body.page .section-cta {
    margin-top: 3rem;
  }
  .section-cta .container {
    min-height: 340px;
  }
  .section-cta .container .content .section-title {
    font-size: 2rem;
    line-height: 1.5em;
  }
}
@media (max-width: 767.98px) {
  .container,
  .container-fluid {
    padding: 0 15px;
  }
  .container-post {
    padding: 0;
  }
  .social-media-list {
    flex-wrap: wrap;
    gap: 5px;
  }
  .default-title {
    font-size: 2rem;
    margin: 1.5rem 0;
  }
  .swiper-arrow-container {
    margin-left: 0;
    width: 100%;
  }
}