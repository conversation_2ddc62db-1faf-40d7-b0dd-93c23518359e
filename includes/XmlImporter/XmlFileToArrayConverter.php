<?php

namespace DD\App\XmlImporter;

use InvalidArgumentException;
use SimpleXMLElement;

class XmlFileToArrayConverter
{
    public function convertStringXmlToArray(string $xmlInString): array
    {
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($xmlInString, SimpleXMLElement::class, LIBXML_NOCDATA);

        if ($xml === false) {
            throw new InvalidArgumentException('Invalid XML content');
        }

        // Check for required structure
        if (!isset($xml->realestate)) {
            throw new InvalidArgumentException('XML structure is incorrect');
        }

        $result = [];
        foreach ($xml->realestate as $realestate) {
            $result[] = $this->convertXmlElementToArray($realestate);
        }

        return $result;
    }

    /**
     * Converts a SimpleXMLElement into an associative array.
     *
     * @param SimpleXMLElement $element
     * @return array
     */
    private function convertXmlElementToArray(SimpleXMLElement $element): array
    {
        return json_decode(json_encode($element), true);
    }
}