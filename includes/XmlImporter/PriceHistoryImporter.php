<?php

namespace DD\App\XmlImporter;

use DateTime;
use DateTimeInterface;
use DD\App\DTO\PriceHistoryDto;
use DD\App\Logger;
use Exception;

/**
 * @see https://voxcrmedu.com/content/19/206/pl/nowa-ustawa-o-jawno%C5%9Bci-cen-_-przekazywanie-historii-cen-na-strone-www.html
 */
class PriceHistoryImporter
{
    public function __construct(private FileReader $fileReader)
    {
    }

    /**
     * @return PriceHistoryDto[]
     *
     * @throws Exception if ID is not matching
     */
    public function getPriceHistory(int $externalId, string $apiUrl): array
    {
        $rawHistory = $this->readXmlFile($apiUrl);

        if ((int) $rawHistory['id'] !== $externalId) {
            throw new Exception('ID is not matching');
        }

        $priceHistory = [];

        if (!isset($rawHistory['price_history']['price_change'])) {
            return [];
        }

        if (isset($rawHistory['price_history']['price_change']['date_modified'])) {
            $rawHistory['price_history']['price_change'] = [$rawHistory['price_history']['price_change']];
        }

        foreach ($rawHistory['price_history']['price_change'] as $rawPriceHistory) {
            $modifiedDate = DateTime::createFromFormat('Y-m-d H:i:s', $rawPriceHistory['date_modified']);

            if ($modifiedDate === false) {
                Logger::error('Failed to import price history - date format is incorrect: ' . $rawPriceHistory['date_modified']);

                continue;
            }

            $element = new PriceHistoryDto(
                $modifiedDate,
                (float) $rawPriceHistory['before']['price'],
                (float) $rawPriceHistory['before']['pricemkw'],
                (float) $rawPriceHistory['after']['price'],
                (float) $rawPriceHistory['after']['pricemkw']
            );

            $priceHistory[] = $element;
        }

        return $priceHistory;
    }

    /**
     * @return array{
     *     id: int,
     *     price_history: array{
     *          price_change?: array<array{
     *              date_modified: string,
     *              before: array{
     *                 price: float,
     *                 pricemkw: float,
     *              },
     *              after: array{
     *                  price: float,
     *                  pricemkw: float,
     *              }
     *          }>
     *     }
     * }
     */
    private function readXmlFile(string $apiUrl): array
    {
        $xmlContent = $this->fileReader->readFile($apiUrl);

        return current((new XmlFileToArrayConverter())->convertStringXmlToArray($xmlContent));
    }
}