<?php

namespace DD\App\XmlImporter;

use DD\App\DTO\PriceHistoryDto;
use DD\App\Enum\Status;
use DD\App\Factory\RealEstateFromXmlFactory;
use DD\App\Logger;
use DD\App\Repository\RealEstateRepository;
use Exception;
use Throwable;
use WP_Term;

class XmlImporter
{
    private RealEstateRepository $realEstateRepository;
    private RealEstateFromXmlFactory $realEstateFromXmlFactory;
    private PriceHistoryImporter $priceHistoryImporter;

    public function __construct()
    {
        $this->realEstateRepository = new RealEstateRepository();
        $this->realEstateFromXmlFactory = new RealEstateFromXmlFactory();
        $this->priceHistoryImporter = new PriceHistoryImporter(new FileReader());
    }

    public function import(): ImportResult
    {
        $xmlConvertedToArray = $this->readXmlFile();

        // Liczba nieudanych importów
        $failedImports = 0;
        $skipped = 0;

        $externalRealEstatesInDatabaseIds = $this->realEstateRepository->getAllRealEstateExternalIds();

        foreach ($xmlConvertedToArray as $realestate) {
            if (in_array((int) $realestate['id'], $externalRealEstatesInDatabaseIds, true)) {
                $skipped++;
                continue;
            }

            try {
                $priceHistory = $this->priceHistoryImporter->getPriceHistory((int) $realestate['id'], $realestate['price_change_history']);
            } catch (Throwable $e) {
                Logger::error('Failed to get price history', $e);

                $failedImports++;
                continue;
            }

            $result = $this->saveRealEstate($realestate, $priceHistory);

            if ($result === false) {
                $failedImports++;
            }
        }

        return new ImportResult(count($xmlConvertedToArray), $skipped, $failedImports);
    }

    /**
     * @return array<array{
     *     id: int,
     *     local_number: string,
     *     status_id: int,
     *     area: float,
     *     floor: int,
     *     price: float,
     *     pricemkw: float,
     *     type_id: int,
     *     balkon: float,
     *     card_link: string,
     *     price_change_history: string,
     *     rooms: string|int
     * }>
     */
    public function readXmlFile(): array
    {
        $filePath = get_option(XmlImporterAction::XML_IMPORT_URL_OPTION, '');
        $xmlContent = file_get_contents($filePath);

        return (new XmlFileToArrayConverter())->convertStringXmlToArray($xmlContent);
    }

    /**
     * @param array{
     *      id: int,
     *      local_number: string,
     *      status_id: string|int,
     *      area: float,
     *      floor: string|int,
     *      price: float,
     *      pricemkw: float,
     *      type_id: string|int,
     *      balkon: float,
     *      card_link: string,
     *      rooms: string|int,
     *  } $realestate
     * @param array<PriceHistoryDto> $priceHistory
     *
     * @return bool
     */
    private function saveRealEstate(array $realestate, array $priceHistory): bool
    {
        $realEstateDto = $this->realEstateFromXmlFactory->createRealEstate($realestate, $priceHistory);

        return $this->realEstateRepository->saveRealEstateInDatabase($realEstateDto);
    }
}