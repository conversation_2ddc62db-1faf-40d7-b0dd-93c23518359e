<?php

use DD\App\Enum\Status;

function dd_register_mieszkania_post_type() {
    $args = [
        'labels' => [
            'name' => 'Nieruchomości',
            'singular_name' => 'Nieruchomość',
            'add_new' => '<PERSON>da<PERSON> nową nieruchomość',
            'add_new_item' => 'Dodaj nową nieruchomość',
            'edit_item' => '<PERSON><PERSON>u<PERSON> nieruchomość',
            'new_item' => 'Nowa nieruchomość',
            'view_item' => 'Zobacz nieruchomość',
            'all_items' => 'Wszystkie nieruchomości',
            'search_items' => 'Szukaj nieruchomości',
            'not_found' => 'Nie znaleziono nieruchomości',
            'not_found_in_trash' => 'Nie znaleziono nieruchomości w koszu',
            'menu_name' => 'Nieruchomości',
        ],
        'public' => true,
        'has_archive' => false,
        'hierarchical' => true,
        'menu_icon' => 'dashicons-building',
        'show_in_rest' => true,
        'supports' => ['title', 'thumbnail'],
        'rewrite' => ['slug' => DD_POST_TYPE],
    ];
    register_post_type(DD_POST_TYPE, $args);
}
add_action('init', 'dd_register_mieszkania_post_type');

function dd_register_and_display_columns($columns) {
    return array_merge(
        array_slice($columns, 0, 2, true),
        [DD_TYPE => 'Typ nieruchomości', DD_STATUS => 'Status'],
        array_slice($columns, 2)
    );
}
add_filter('manage_' . DD_POST_TYPE . '_posts_columns', 'dd_register_and_display_columns');

function dd_display_column_values($column, $post_id) {
    if ($column === DD_TYPE) {
        echo DD_TYPES[get_post_meta($post_id, DD_TYPE, true)] ?? 'Brak typu';
    } elseif ($column === DD_STATUS) {
        echo Status::tryFrom(get_post_meta($post_id, DD_STATUS, true))?->value ?? 'Brak statusu';
    }
}
add_action('manage_' . DD_POST_TYPE . '_posts_custom_column', 'dd_display_column_values', 10, 2);

// Sortowania dla kolumn
function dd_sortable_columns($columns) {
    $columns[DD_TYPE] = DD_TYPE;
    $columns[DD_STATUS] = DD_STATUS;
    return $columns;
}
add_filter('manage_edit-' . DD_POST_TYPE . '_sortable_columns', 'dd_sortable_columns');

// Sortowanie wyników
function dd_sort_property_type_and_status($query) {
    if (is_admin() && $query->is_main_query()) {
        $orderby = $query->get('orderby');
        if ($orderby === DD_TYPE || $orderby === DD_STATUS) {
            $query->set('meta_key', $orderby);
            $query->set('orderby', 'meta_value');
        }
    }
}
add_action('pre_get_posts', 'dd_sort_property_type_and_status');

// Zapisanie meta
function dd_save_property_meta($post_id) {
    foreach ([DD_TYPE, DD_STATUS] as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post_' . DD_POST_TYPE, 'dd_save_property_meta');

// Filtry
function dd_add_property_filters() {
    global $typenow;

    if ($typenow !== DD_POST_TYPE) {
        return;
    }

    $statusOptions = [
        Status::Dostepne->value => Status::Dostepne->displayName(),
        Status::Rezerwacja->value => Status::Rezerwacja->displayName(),
        Status::Sprzedane->value => Status::Sprzedane->displayName(),
    ];

    foreach ([
        ['name' => 'typ', 'options' => DD_TYPES],
        ['name' => 'status', 'options' => $statusOptions],
    ] as $filter) {
        echo "<select name=\"{$filter['name']}\" class=\"postform\"><option value=\"\">Wszystkie {$filter['name']}y</option>";
        foreach ($filter['options'] as $slug => $name) {
            echo "<option value=\"$slug\" " . selected($slug, $_GET[$filter['name']] ?? '', false) . ">$name</option>";
        }
        echo '</select>';
    }
}
add_action('restrict_manage_posts', 'dd_add_property_filters');

// Filtrowanie status i typ
function dd_filter_property_type_and_status($query) {
    if (is_admin() && $query->is_main_query()) {
        foreach (['typ' => DD_TYPE, 'status' => DD_STATUS] as $filter_key => $meta_key) {
            if (!empty($_GET[$filter_key])) {
                $query->set('meta_key', $meta_key);
                $query->set('meta_value', sanitize_text_field($_GET[$filter_key]));
            }
        }
    }
}
add_filter('pre_get_posts', 'dd_filter_property_type_and_status');

function dd_register_udogodnienia_taxonomies() {
    register_taxonomy(DD_FACILITIES, DD_POST_TYPE, [
        'label' => 'Udogodnienia',
        'public' => true,
        'hierarchical' => true,
        'show_ui'           => true,
        'show_admin_column' => true,
    ]);
}
add_action('init', 'dd_register_udogodnienia_taxonomies');

function dd_add_featured_properties_page() {
    add_submenu_page(
        'edit.php?post_type=' . DD_POST_TYPE,
        'Polecane mieszkania',
        'Polecane mieszkania',
        'manage_options',
        'dd-featured-properties',
        'dd_featured_properties_page_callback'
    );
}
add_action('admin_menu', 'dd_add_featured_properties_page');

function dd_get_featured_properties() {
    $featured_properties = get_option(DD_FEATURED_PROPERTIES, []);
    $saved_properties = get_option(DD_SAVE_FEATURED_PROPERTIES, []);
    
    $missing_properties = max(4 - count($featured_properties), 0);

    if (count($saved_properties) !== $missing_properties) {
        $new_properties = dd_fetch_saved_properties($missing_properties, array_merge($featured_properties, $saved_properties));
        
        if (!empty($new_properties)) {
            update_option(DD_SAVE_FEATURED_PROPERTIES, array_merge($saved_properties, $new_properties));
            $saved_properties = array_slice(array_merge($saved_properties, $new_properties), 0, $missing_properties);
        }
    }

    return array_merge($featured_properties, $saved_properties);
}

function dd_fetch_saved_properties($count, $exclude = []) {
    return $count > 0 ? get_posts([
        'post_type'      => DD_POST_TYPE,
        'posts_per_page' => $count,
        'orderby'        => 'rand',
        'post__not_in'   => $exclude,
        'fields'         => 'ids',
        'meta_query'     => [
            [
                'key'     => DD_TYPE,
                'value'   => DD_TYPE_MIESZKANIE,
                'compare' => '='
            ]
        ]
    ]) : [];
}

function dd_featured_properties_page_callback() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $selected_properties = !empty($_POST[DD_FEATURED_PROPERTIES]) ? (array) $_POST[DD_FEATURED_PROPERTIES] : [];
        update_option(DD_FEATURED_PROPERTIES, array_map('intval', (array) $selected_properties));
        delete_option(DD_SAVE_FEATURED_PROPERTIES);
    }

    $selected_properties = get_option(DD_FEATURED_PROPERTIES, []);
    $all_properties = get_posts([
        'post_type'      => DD_POST_TYPE,
        'posts_per_page' => -1,
        'fields'         => 'ids',
        'meta_query'     => [
            [
                'key'     => DD_TYPE,
                'value'   => DD_TYPE_MIESZKANIE,
                'compare' => '='
            ]
        ]
    ]);

    $selected_properties = dd_get_featured_properties();
    ?>
<h1>Polecane Mieszkania</h1>
<div class="left-right">
    <div class="left">
        <form method="post">
            <h2>Wybierz mieszkania</h2>
            <div class="form-table">
                <select name="<?php echo esc_attr(DD_FEATURED_PROPERTIES); ?>[]" multiple
                    id="dd_featured_properties_select">
                    <?php foreach ($all_properties as $property_id): ?>
                    <option value="<?php echo $property_id ?>"
                        <?php selected(in_array($property_id, $selected_properties, true)); ?>>
                        <?php echo esc_html(get_the_title($property_id)); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php submit_button('Zapisz zmiany', 'primary', 'submit'); ?>
        </form>
    </div>
    <div class="right">
        <h2>Wybrane Mieszkania</h2>
        <?php if (!empty($selected_properties)): ?>
        <ul>
            <?php foreach ($selected_properties as $property_id): ?>
            <li>
                <a href="<?php echo esc_url(get_edit_post_link($property_id)); ?>">
                    <?php echo esc_html(get_the_title($property_id)); ?>
                </a>
            </li>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
</div>
<script>
jQuery(document).ready(function($) {
    $('#dd_featured_properties_select').select2({
        placeholder: 'Wybierz mieszkania',
        allowClear: true
    });
});
</script>
<?php
}