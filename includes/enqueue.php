<?php

function load_stylesheets() {

  wp_register_style('style', get_template_directory_uri() . '/assets/css/style.css', array(), 1, 'all');
  wp_enqueue_style('style');

  wp_register_style( 'swiper', get_template_directory_uri() . '/assets/css/swiper.css', array(), 1, 'all' );
	wp_enqueue_style( 'swiper' );

}

add_action('wp_enqueue_scripts', 'load_stylesheets');

function load_js() {

  wp_deregister_script( 'jquery' );

	wp_register_script( 'jquery', get_template_directory_uri() . '/assets/js/jquery.js', array(), 1, 1, 1 );
	wp_enqueue_script( 'jquery' );

	wp_register_script( 'swiper', get_template_directory_uri() . '/assets/js/swiper.js', array(), 1, 0, 1 );
	wp_enqueue_script( 'swiper' );

  wp_register_script('script', get_template_directory_uri() . '/assets/js/script.js', array(), 1, 1, 1);
  wp_enqueue_script('script');

  wp_localize_script('script', 'my_ajax_object', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce'    => wp_create_nonce('my_ajax_nonce'),
  ));
  
}

add_action('wp_enqueue_scripts', 'load_js');