<?php

use DD\App\Enum\Status;

require_once plugin_dir_path(__FILE__) . 'upload-handler.php';

function dd_add_mieszkania_meta_boxes() {
    add_meta_box(
        'mieszkanie_details',
        __('Szczegóły Mieszkania'),
        'dd_render_mieszkania_meta_box',
        DD_POST_TYPE,
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'dd_add_mieszkania_meta_boxes');

function dd_render_mieszkania_meta_box($post) {
    wp_nonce_field('save_mieszkania_details', 'mieszkania_details_nonce');

    $meta_fields = [
        DD_LOCATION => get_post_meta($post->ID, DD_LOCATION, true),
        DD_PRICE => get_post_meta($post->ID, DD_PRICE, true),
        DD_PRICE_PER_METER => get_post_meta($post->ID, DD_PRICE_PER_METER, true),
        DD_STATUS => get_post_meta($post->ID, DD_STATUS, true),
        DD_IMAGES => get_post_meta($post->ID, DD_IMAGES, true),
        DD_PDF_LINK => get_post_meta($post->ID, DD_PDF_LINK, true),
        DD_3D_LINK => get_post_meta($post->ID, DD_3D_LINK, true),
        DD_FLOOR => get_post_meta($post->ID, DD_FLOOR, true),
        DD_SQUARE => get_post_meta($post->ID, DD_SQUARE, true),
        DD_ROOMS => get_post_meta($post->ID, DD_ROOMS, true),
        DD_BUILDING_NUMBER => get_post_meta($post->ID, DD_BUILDING_NUMBER, true),
        DD_ID => get_post_meta($post->ID, DD_ID, true),
        DD_TYPE => get_post_meta($post->ID, DD_TYPE, true),
        DD_VERTICAL_SHOT => get_post_meta($post->ID, DD_VERTICAL_SHOT, true),
        DD_ANGLED_SHOT => get_post_meta($post->ID, DD_ANGLED_SHOT, true),
        DD_EXTERNAL_ID => get_post_meta($post->ID, DD_EXTERNAL_ID, true),
    ];

    echo "<label for='" . DD_LOCATION . "'>ID w VOX CRM: </label>";
    echo "<input type='text' id='" . DD_LOCATION . "' disabled value='" . esc_attr($meta_fields[DD_EXTERNAL_ID]) . "' />";

    echo "<label for='" . DD_TYPE . "'>Typ nieruchomości</label>";
    echo "<select id='" . DD_TYPE . "' name='" . DD_TYPE . "' />";
    foreach (DD_TYPES as $key => $label) {
        $selected = ($meta_fields[DD_TYPE] === $key) ? 'selected' : '';
        echo "<option value='$key' $selected>$label</option>";
    }
    echo "</select>";

    echo "<label for='" . DD_LOCATION . "'>Lokalizacja</label>";
    echo "<input type='text' id='" . DD_LOCATION . "' name='" . DD_LOCATION . "' value='" . esc_attr($meta_fields[DD_LOCATION]) . "' />";
    
    echo "<label for='" . DD_PRICE . "'>Cena</label>";
    echo "<input type='number' id='" . DD_PRICE . "' name='" . DD_PRICE . "' value='" . esc_attr($meta_fields[DD_PRICE]) . "' />";

    echo "<label for='" . DD_PRICE_PER_METER . "'>Cena za metr</label>";
    echo "<input type='number' id='" . DD_PRICE_PER_METER . "' name='" . DD_PRICE_PER_METER . "' value='" . esc_attr($meta_fields[DD_PRICE_PER_METER]) . "' />";

    echo "<label for='" . DD_ID . "'>Numer identyfikacyjny</label>";
    echo "<input type='text' id='" . DD_ID . "' name='" . DD_ID . "' value='" . esc_attr($meta_fields[DD_ID]) . "' />";
    
    echo "<label for='" . DD_SQUARE . "'>Powierzchnia (w m2)</label>";
    echo "<input type='number' step='0.01' id='" . DD_SQUARE . "' name='" . DD_SQUARE . "' value='" . esc_attr($meta_fields[DD_SQUARE]) . "' />";
    
    echo "<label for='" . DD_ROOMS . "'>Liczba pokoi</label>";
    echo "<input type='number' id='" . DD_ROOMS . "' name='" . DD_ROOMS . "' value='" . esc_attr($meta_fields[DD_ROOMS]) . "' />";
    
    echo "<label for='" . DD_FLOOR . "'>Numer piętra</label>";
    echo "<input type='number' id='" . DD_FLOOR . "' name='" . DD_FLOOR . "' value='" . esc_attr($meta_fields[DD_FLOOR]) . "' />";
    
    echo "<label for='" . DD_BUILDING_NUMBER . "'>Numer budynku</label>";
    echo "<input type='text' id='" . DD_BUILDING_NUMBER . "' name='" . DD_BUILDING_NUMBER . "' value='" . esc_attr($meta_fields[DD_BUILDING_NUMBER]) . "' />";
    
    echo "<label for='" . DD_PDF_LINK . "'>Link do karty lokalu (PDF):</label>";
    echo "<input type='text' id='" . DD_PDF_LINK . "' name='" . DD_PDF_LINK . "' value='" . esc_attr($meta_fields[DD_PDF_LINK]) . "' />";
    
    echo "<label for='" . DD_3D_LINK . "'>Link do spaceru 3D:</label>";
    echo "<input type='text' id='" . DD_3D_LINK . "' name='" . DD_3D_LINK . "' value='" . esc_attr($meta_fields[DD_3D_LINK]) . "' />";

    echo '<div class="taxonomies">';
        echo "<label for='" . DD_STATUS . "'>Dostępność:</label>";
        echo "<select id='" . DD_STATUS . "' name='" . DD_STATUS . "'>";
        foreach (Status::cases() as $status) {
            $selected = ($meta_fields[DD_STATUS] === $status->value) ? 'selected' : '';
            echo "<option value='" . esc_attr($status->value) . "' $selected>" . esc_html($status->displayName()) . "</option>";
        }
        echo "</select>";
    echo '</div>';
    
    echo '<div class="images">';
        echo '  <input type="hidden" id="angled_shot" name="' . DD_ANGLED_SHOT . '" value="' . esc_attr($meta_fields[DD_ANGLED_SHOT]) . '" />';
            echo '  <div id="angled_preview">';
            echo '  <input type="button" id="upload_angled_button" class="button" value="Wybierz rzut ukośny" />';
            if (!empty($meta_fields[DD_ANGLED_SHOT]) && filter_var($meta_fields[DD_ANGLED_SHOT], FILTER_VALIDATE_URL)) {
                echo '<img src="' . esc_url($meta_fields[DD_ANGLED_SHOT]) . '" style="max-width: 150px;"/>';
                echo '<input type="button" id="remove_angled_shot" class="button" value="Usuń zdjęcie" />';
            }
        echo '</div>';

        echo '  <input type="hidden" id="vertical_shot" name="' . DD_VERTICAL_SHOT . '" value="' . esc_attr($meta_fields[DD_VERTICAL_SHOT]) . '" />';
            echo '  <div id="vertical_preview">';
            echo '  <input type="button" id="upload_vertical_button" class="button" value="Wybierz rzut pionowy" />';
            if (!empty($meta_fields[DD_VERTICAL_SHOT]) && filter_var($meta_fields[DD_VERTICAL_SHOT], FILTER_VALIDATE_URL)) {
                echo '<img src="' . esc_url($meta_fields[DD_VERTICAL_SHOT]) . '" style="max-width: 150px;"/>';
                echo '<input type="button" id="remove_vertical_shot" class="button" value="Usuń zdjęcie" />';
            }
    echo '</div>';
}

function dd_save_mieszkania_meta_boxes($post_id) {
    if (!isset($_POST['mieszkania_details_nonce']) || !wp_verify_nonce($_POST['mieszkania_details_nonce'], 'save_mieszkania_details')) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST[DD_FLOOR])) {
        if (!is_numeric($_POST[DD_FLOOR]) || $_POST[DD_FLOOR] < -1) {
            wp_admin_notice('Piętro musi być liczbą całkowitą większą lub równą -1.', ['type' => 'error']);
            return;
        }
    }
    
    $meta_fields = [
        DD_LOCATION => sanitize_text_field($_POST[DD_LOCATION] ?? ''),
        DD_PRICE => floatval($_POST[DD_PRICE] ?? 0),
        DD_PRICE_PER_METER => floatval($_POST[DD_PRICE_PER_METER] ?? 0),
        DD_STATUS => isset($_POST[DD_STATUS]) ? strtolower(sanitize_text_field($_POST[DD_STATUS])) : '',
        DD_VERTICAL_SHOT => !empty($_POST[DD_VERTICAL_SHOT]) ? esc_url_raw($_POST[DD_VERTICAL_SHOT]) : '',
        DD_ANGLED_SHOT => !empty($_POST[DD_ANGLED_SHOT]) ? esc_url_raw($_POST[DD_ANGLED_SHOT]) : '',
        DD_PDF_LINK => sanitize_text_field($_POST[DD_PDF_LINK] ?? ''),
        DD_3D_LINK => sanitize_text_field($_POST[DD_3D_LINK] ?? ''),
        DD_FLOOR => (int) $_POST[DD_FLOOR],
        DD_SQUARE => (float) ($_POST[DD_SQUARE] ?? 0),
        DD_ROOMS => absint($_POST[DD_ROOMS] ?? 0),
        DD_BUILDING_NUMBER => sanitize_text_field($_POST[DD_BUILDING_NUMBER] ?? ''),
        DD_ID => sanitize_text_field($_POST[DD_ID] ?? ''),
        DD_TYPE => sanitize_text_field($_POST[DD_TYPE] ?? ''),
    ];

    foreach ($meta_fields as $key => $value) {
        update_post_meta($post_id, $key, $value);
    }

    if (!empty($meta_fields[DD_STATUS])) {
        wp_set_object_terms($post_id, $meta_fields[DD_STATUS], DD_STATUS);
    }

    if (!empty($meta_fields[DD_TYPE])) {
        wp_set_object_terms($post_id, $meta_fields[DD_TYPE], DD_TYPE);
    }
}

add_action('save_post', 'dd_save_mieszkania_meta_boxes');

?>