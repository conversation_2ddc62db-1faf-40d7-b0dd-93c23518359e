<?php

namespace DD\App;

class BulkPhotoUploadAction
{
    public static function init(): void
    {
        add_action('admin_menu', function () {
            add_submenu_page(
                'edit.php?post_type=' . DD_POST_TYPE,
                'Masowa zmiana zdj<PERSON>',
                'Masowa zmiana zdj<PERSON>',
                'manage_options',
                'dd-bulk-photos',
                [self::class, 'uploadPage']
            );
        });
        add_action('wp_ajax_bulk_photos_upload', [self::class, 'ajaxUploadAction']);
    }

    public static function uploadPage(): void
    {
        echo '<div class="wrap">';
        echo '<h1>Bulk upload zdjęć dla mieszkań</h1>';
        echo '<p>Ta strona pozwala na masowe załadowanie zdjęć rzutów pionowych i ukośnych dla mieszkań.</p>';
        echo '<p>Nazwy plików powinny z<PERSON> się od identyfikatora mieszkania (ID).</p>';

        echo '<div class="bulk-upload-section">';
        echo '<h2>R<PERSON>ty pionowe (Vertical shot)</h2>';
        echo '<p>Wybierz wszystkie pliki rzutów pionowych dla mieszkań.</p>';
        echo '<button id="vertical-upload-button" class="button button-primary">Wybierz pliki rzutów pionowych</button>';
        echo '<div id="vertical-upload-progress" style="display:none; margin-top: 10px;">';
        echo '<p>Postęp: <span id="vertical-progress-count">0</span> / <span id="vertical-total-count">0</span></p>';
        echo '<div class="progress-bar"><div id="vertical-progress-bar" style="width: 0%; height: 20px; background-color: #0073aa;"></div></div>';
        echo '</div>';
        echo '<div id="vertical-results" style="margin-top: 10px;"></div>';
        echo '</div>';

        echo '<div class="bulk-upload-section" style="margin-top: 30px;">';
        echo '<h2>Rzuty ukośne (Angled shot)</h2>';
        echo '<p>Wybierz wszystkie pliki rzutów ukośnych dla mieszkań.</p>';
        echo '<button id="angled-upload-button" class="button button-primary">Wybierz pliki rzutów ukośnych</button>';
        echo '<div id="angled-upload-progress" style="display:none; margin-top: 10px;">';
        echo '<p>Postęp: <span id="angled-progress-count">0</span> / <span id="angled-total-count">0</span></p>';
        echo '<div class="progress-bar"><div id="angled-progress-bar" style="width: 0%; height: 20px; background-color: #0073aa;"></div></div>';
        echo '</div>';
        echo '<div id="angled-results" style="margin-top: 10px;"></div>';
        echo '</div>';

        echo '</div>'; // .wrap

        self::includeJavaScript();
    }

    private static function includeJavaScript(): void
    {
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Setup media uploader for vertical shots
                $('#vertical-upload-button').click(function(e) {
                    e.preventDefault();

                    var verticalUploader = wp.media({
                        title: 'Wybierz pliki rzutów pionowych',
                        button: {
                            text: 'Użyj tych plików'
                        },
                        multiple: true
                    });

                    verticalUploader.on('select', function() {
                        var attachments = verticalUploader.state().get('selection').toJSON();
                        processFiles(attachments, 'vertical');
                    });

                    verticalUploader.open();
                });

                // Setup media uploader for angled shots
                $('#angled-upload-button').click(function(e) {
                    e.preventDefault();

                    var angledUploader = wp.media({
                        title: 'Wybierz pliki rzutów ukośnych',
                        button: {
                            text: 'Użyj tych plików'
                        },
                        multiple: true
                    });

                    angledUploader.on('select', function() {
                        var attachments = angledUploader.state().get('selection').toJSON();
                        processFiles(attachments, 'angled');
                    });

                    angledUploader.open();
                });

                function processFiles(attachments, type) {
                    // Show progress UI
                    $('#' + type + '-upload-progress').show();
                    $('#' + type + '-total-count').text(attachments.length);
                    $('#' + type + '-progress-count').text(0);
                    $('#' + type + '-progress-bar').css('width', '0%');
                    $('#' + type + '-results').empty();

                    var processed = 0;
                    var results = {
                        success: 0,
                        failed: 0,
                        skipped: 0,
                        details: []
                    };

                    // Process each file sequentially
                    function processNextFile(index) {
                        if (index >= attachments.length) {
                            // All files processed
                            showResults(results, type);
                            return;
                        }

                        var file = attachments[index];
                        var fileName = file.filename;

                        // Extract property ID from filename
                        var idMatch = fileName.match(/^([A-Za-z0-9]+)/);
                        var propertyId = idMatch ? idMatch[1] : null;

                        if (!propertyId) {
                            results.failed++;
                            results.details.push({
                                file: fileName,
                                status: 'failed',
                                message: 'Nie można rozpoznać ID mieszkania z nazwy pliku'
                            });
                            updateProgress(++processed, attachments.length, type);
                            processNextFile(index + 1);
                            return;
                        }

                        // Send to server for processing
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'bulk_photos_upload',
                                nonce: '<?php echo wp_create_nonce('bulk_photos_upload_nonce'); ?>',
                                attachment_id: file.id,
                                property_id: propertyId,
                                shot_type: type
                            },
                            success: function(response) {
                                if (response.success) {
                                    results.success++;
                                    results.details.push({
                                        file: fileName,
                                        status: 'success',
                                        message: response.data.message,
                                        post_title: response.data.post_title
                                    });
                                } else {
                                    if (response.data.status === 'skipped') {
                                        results.skipped++;
                                    } else {
                                        results.failed++;
                                    }
                                    results.details.push({
                                        file: fileName,
                                        status: response.data.status,
                                        message: response.data.message
                                    });
                                }
                                updateProgress(++processed, attachments.length, type);
                                processNextFile(index + 1);
                            },
                            error: function() {
                                results.failed++;
                                results.details.push({
                                    file: fileName,
                                    status: 'failed',
                                    message: 'Błąd serwera podczas przetwarzania pliku'
                                });
                                updateProgress(++processed, attachments.length, type);
                                processNextFile(index + 1);
                            }
                        });
                    }

                    // Start processing
                    processNextFile(0);
                }

                function updateProgress(current, total, type) {
                    var percentage = Math.round((current / total) * 100);
                    $('#' + type + '-progress-count').text(current);
                    $('#' + type + '-progress-bar').css('width', percentage + '%');
                }

                function showResults(results, type) {
                    var $results = $('#' + type + '-results');

                    $results.append('<h3>Wyniki:</h3>');
                    $results.append('<p>Pomyślnie przetworzono: ' + results.success + '</p>');
                    $results.append('<p>Pominięto: ' + results.skipped + '</p>');
                    $results.append('<p>Nie udało się: ' + results.failed + '</p>');

                    if (results.details.length > 0) {
                        var $table = $('<table class="widefat striped">');
                        $table.append('<thead><tr><th>Plik</th><th>Status</th><th>Mieszkanie</th><th>Informacja</th></tr></thead>');
                        var $tbody = $('<tbody>');

                        $.each(results.details, function(i, detail) {
                            var statusClass = '';
                            var statusText = '';

                            if (detail.status === 'success') {
                                statusClass = 'success';
                                statusText = 'Sukces';
                            } else if (detail.status === 'skipped') {
                                statusClass = 'skipped';
                                statusText = 'Pominięto';
                            } else {
                                statusClass = 'failed';
                                statusText = 'Błąd';
                            }

                            var $row = $('<tr>');
                            $row.append('<td>' + detail.file + '</td>');
                            $row.append('<td class="' + statusClass + '">' + statusText + '</td>');
                            $row.append('<td>' + (detail.post_title || '-') + '</td>');
                            $row.append('<td>' + detail.message + '</td>');

                            $tbody.append($row);
                        });

                        $table.append($tbody);
                        $results.append($table);
                    }

                    $results.append('<style>.success{color:green;}.failed{color:red;}.skipped{color:orange;}.progress-bar{width:100%;background-color:#eee;border-radius:3px;overflow:hidden;}</style>');
                }
            });
        </script>
        <?php
    }

    public static function ajaxUploadAction(): void
    {
        check_ajax_referer('bulk_photos_upload_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Brak uprawnień', 'status' => 'failed'], 403);
        }

        $attachment_id = isset($_POST['attachment_id']) ? intval($_POST['attachment_id']) : 0;
        $property_id = isset($_POST['property_id']) ? sanitize_text_field($_POST['property_id']) : '';
        $shot_type = isset($_POST['shot_type']) ? sanitize_text_field($_POST['shot_type']) : '';

        if (empty($attachment_id) || empty($property_id) || empty($shot_type)) {
            wp_send_json_error([
                'message' => 'Nieprawidłowe dane wejściowe',
                'status' => 'failed'
            ]);
        }

        // Validate shot type
        if ($shot_type !== 'vertical' && $shot_type !== 'angled') {
            wp_send_json_error([
                'message' => 'Nieprawidłowy typ zdjęcia',
                'status' => 'failed'
            ]);
        }

        // Find post with matching ID
        $posts = get_posts([
            'post_type' => DD_POST_TYPE,
            'posts_per_page' => 1,
            'meta_query' => [
                [
                    'key' => DD_ID,
                    'value' => $property_id,
                    'compare' => '='
                ],
                [
                    'key' => DD_TYPE,
                    'value' => DD_TYPE_MIESZKANIE,
                    'compare' => '='
                ]
            ]
        ]);

        if (empty($posts)) {
            wp_send_json_error([
                'message' => 'Nie znaleziono mieszkania o ID: ' . $property_id,
                'status' => 'failed'
            ]);
        }

        $post = $posts[0];
        $post_id = $post->ID;
        $meta_key = $shot_type === 'vertical' ? DD_VERTICAL_SHOT : DD_ANGLED_SHOT;

        // Get attachment URL
        $attachment_url = wp_get_attachment_url($attachment_id);
        if (!$attachment_url) {
            wp_send_json_error([
                'message' => 'Nie można uzyskać URL załącznika',
                'status' => 'failed'
            ]);
        }

        // Update post meta
        update_post_meta($post_id, $meta_key, $attachment_url);

        wp_send_json_success([
            'message' => 'Pomyślnie dodano zdjęcie ' . ($shot_type === 'vertical' ? 'pionowe' : 'ukośne'),
            'post_title' => $post->post_title
        ]);
    }
}