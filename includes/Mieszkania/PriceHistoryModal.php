<?php

namespace DD\App\Mieszkania;

use DD\App\DTO\PriceHistoryDto;

class PriceHistoryModal
{
    /**
     * @param array<PriceHistoryDto> $priceHistory
     *
     * @return string HTML
     */
    public static function renderPriceHistory(array $priceHistory): string
    {
        ob_start();
        ?>
        <div class="dd-price-history-modal-overlay">
            <div class="dd-price-history-modal" role="dialog" aria-modal="true" aria-labelledby="price-history-title">
                 <button class="dd-price-history-modal-close" aria-label="Zamknij modal">
                    <svg fill="none" height="21" viewBox="0 0 20 21" width="20" xmlns="http://www.w3.org/2000/svg">
                        <path clip-rule="evenodd" d="m10 20.6008c5.5228 0 10-4.4772 10-10 0-5.52288-4.4772-10.000031-10-10.000031-5.52285 0-10 4.477151-10 10.000031 0 5.5228 4.47715 10 10 10zm-2.40409-13.53557c-.31242-.31242-.81895-.31242-1.13137 0s-.31242.81895 0 1.13137l2.4042 2.4042-2.40417 2.4042c-.31242.3124-.31242.8189 0 1.1313.31242.3125.81895.3125 1.13137 0l2.40416-2.4041 2.4041 2.4041c.3125.3124.819.3124 1.1314 0s.3124-.819 0-1.1314l-2.4041-2.4041 2.4041-2.40416c.3125-.31242.3125-.81895 0-1.13137-.3124-.31242-.8189-.31242-1.1313 0l-2.4042 2.40416z" fill="#e3e3e3" fill-rule="evenodd"/>
                    </svg>
                </button>
                </button>
                <h2 class="modal-title">Historia zmiany cen lokalu</h2>
                <div class="table-wrapper">
                <?php if ($priceHistory === []): ?>
                    <p>Cena tego lokalu nie była zmieniana od 11 lipca 2025.</p>
                <?php else: ?>
                    <table>
                        <thead>
                            <tr>
                                <th>Data zmiany</th>
                                <th>Cena po zmianie</th>
                                <th>Cena przed zmianą</th>
                                <th>Cena po zmianie za m²</th>
                                <th>Cena przed zmianą za m²</th>
                            </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($priceHistory as $history): ?>
                            <tr>
                                <td><?= esc_html($history->modifiedDate->format('d.m.Y H:i')) ?></td>
                                <td><?= esc_html(number_format($history->afterPrice, 0, '', ' ')) ?> zł</td>
                                <td><?= esc_html(number_format($history->beforePrice, 0, '', ' ')) ?> zł</td>
                                <td><?= esc_html(number_format($history->afterPerMeterPrice, 0, '', ' ')) ?> zł / m²</td>
                                <td><?= esc_html(number_format($history->beforePerMeterPrice, 0, '', ' ')) ?> zł / m²</td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
