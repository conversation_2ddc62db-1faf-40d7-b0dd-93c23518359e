<?php

namespace DD\App\FromXmlUpdater;

class UpdateResult
{
    public int $inXml = 0;
    public int $updated = 0;
    public int $skipped = 0;
    public int $failed = 0;
    public int $notFoundInDatabase = 0;

    public function __construct(int $inXml, int $updated, int $skipped, int $failed, int $notFoundInDatabase)
    {
        $this->inXml = $inXml;
        $this->updated = $updated;
        $this->skipped = $skipped;
        $this->failed = $failed;
        $this->notFoundInDatabase = $notFoundInDatabase;
    }
}
