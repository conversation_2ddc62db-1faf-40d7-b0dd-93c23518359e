<?php

namespace DD\App\FromXmlUpdater;

use DD\App\XmlImporter\XmlImporterAction;

class UpdateFromXmlAction
{
    private const HOOK_NAME = 'dd_update_from_xml_action';

    public static function init(): void
    {
        add_action(self::HOOK_NAME, [self::class, 'execute']);

        if (!wp_next_scheduled( self::HOOK_NAME ) ) {
            wp_schedule_event(1735686000, 'twicedaily', self::HOOK_NAME );
        }
    }

    public static function execute(): void
    {
        $updatingEnabled = (bool) get_option(XmlImporterAction::XML_AUTO_UPDATING_OPTION);

        if ($updatingEnabled === false) {
            return;
        }

        $updater = new FromXmlUpdater();
        $updater->update();
    }
}