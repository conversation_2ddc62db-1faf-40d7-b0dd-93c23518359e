<?php

namespace DD\App\Enum;

enum Type: string
{
    case Mieszkanie = 'mieszkanie';
    case KomorkaLokatorska = 'komorka-lokatorska';
    case Garaz = 'miejsce-postojowe-garaz';
    case ParkingZewnatrz = 'miejsce-postojowe-na-zewnatrz';
    case LokalUslugowy = 'lokal-uslugowy';

    public function displayName(): string
    {
        return match ($this) {
            self::Mieszkanie => 'Mieszkanie',
            self::KomorkaLokatorska => 'Komórka lokatorska',
            self::Garaz => 'Miejsce postojowe - garaż',
            self::ParkingZewnatrz => 'Miejsce postojowe - na zewnątrz',
            self::LokalUslugowy => 'Lokal usługowy',
        };
    }
}
