<?php

namespace DD\App\Enum;

enum SortDirection: string
{
    case ASC = 'asc';
    case DESC = 'desc';

    public function displayName(): string
    {
        return match ($this) {
            self::ASC => 'Rosnąco',
            self::DESC => 'Malejąco',
        };
    }

    public function getOpposite(): self
    {
        return match ($this) {
            self::ASC => self::DESC,
            self::DESC => self::ASC,
        };
    }
}
