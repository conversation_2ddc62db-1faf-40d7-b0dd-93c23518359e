<?php

namespace DD\App\Enum;

enum SortField: string
{
    case PRICE = 'price';
    case AREA = 'area';

    public function displayName(): string
    {
        return match ($this) {
            self::PRICE => 'Cena',
            self::AREA => 'Powierzchnia',
        };
    }

    public function getColumnName(): string
    {
        return match ($this) {
            self::PRICE => 'price',
            self::AREA => 'area',
        };
    }
}
