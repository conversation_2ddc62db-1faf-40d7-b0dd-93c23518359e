<?php

use DD\App\Enum\Status;

function dd_register_realestate_endpoints()

{
    register_rest_route('dd/v2', '/realestate/', [
        'methods'  => 'GET',
        'callback' => 'dd_get_realestate',
        'permission_callback' => '__return_true',
    ]);
}
add_action('rest_api_init', 'dd_register_realestate_endpoints');

function dd_get_realestate(WP_REST_Request $data): WP_REST_Response 
{
    global $wpdb;

    $sql = sprintf('WHERE posts.post_type = "%s"', DD_POST_TYPE);

    if (!empty($data['list_of_ids'])) {
        $ids = array_map('absint', explode(',', $data['list_of_ids']));
        $idsToSearch = implode(',', $ids);
        $sql .= " AND posts.ID IN ($idsToSearch)";
    }

    elseif (isset($data['floor'])) {
        $floor = intval($data['floor']);
        if ($floor < -1) {
            return rest_ensure_response([]);    
        }

        $sql .= " AND EXISTS (
            SELECT 1 FROM {$wpdb->postmeta} posts_meta WHERE posts_meta.post_id = posts.ID 
            AND posts_meta.meta_key = \"". DD_FLOOR ."\" AND posts_meta.meta_value = $floor
        )";
    } else {
        return rest_ensure_response([]);
    }

    $query = $wpdb->prepare("
    SELECT 
        posts.ID as id, 
        posts.guid as url,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS status,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS property_type,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS id_number,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS building_number,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS pricemkw,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS size,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS floor,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS rooms,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS vertical_shot,
        MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS angled_shot
    FROM {$wpdb->posts} posts
    LEFT JOIN {$wpdb->postmeta} posts_meta ON posts.ID = posts_meta.post_id
    $sql
    GROUP BY posts.ID
    ", 
    DD_STATUS, DD_TYPE, DD_ID, DD_BUILDING_NUMBER, DD_PRICE, DD_PRICE_PER_METER, DD_SQUARE, DD_FLOOR, DD_ROOMS, 
    DD_VERTICAL_SHOT, DD_ANGLED_SHOT);

    $results = $wpdb->get_results($query);

    foreach ($results as &$realestate) {
        if (isset(DD_TYPES[$realestate->property_type])) {
            $realestate->property_type = DD_TYPES[$realestate->property_type];
        }

        $realestate->status_display_name = Status::from($realestate->status)->displayName();

        $facilities = wp_get_post_terms($realestate->id, DD_FACILITIES);
        $realestate->facilities = array_map(fn($facility) => [
            'id' => $facility->term_id,
            'name' => $facility->name
        ], $facilities);
    }

    return rest_ensure_response($results);
}