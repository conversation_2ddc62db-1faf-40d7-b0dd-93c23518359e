<?php

register_nav_menus(
	array(
		'main-menu' => __( 'Główne', 'theme' ),
		'footer-menu-1' => __( 'Kontakt', 'theme' ),
		'footer-menu-2' => __( 'Adres', 'theme' ),
		'footer-menu-3' => __( 'Numery telefonu', 'theme' ),
		'footer-menu-4' => __( 'E-mail', 'theme' ),
        'footer-menu-5' => __( 'Pod stopką', 'theme' ),
	)
);

function mytheme_setup() {
    // Dodaj wsparcie dla logo w nagłówku
    add_theme_support('custom-logo', array(
        'width'      => 300,
        'height'     => 100,
        'flex-width' => true,
        'flex-height' => true,
    ));

    // Dodaj wsparcie dla nagłówka
    add_theme_support('custom-header', array(
        'width'          => 1200,
        'height'         => 280,
        'flex-width'     => true,
        'flex-height'    => true,
    ));

    // Logo w stopce
    add_action('customize_register', function( $wp_customize ) {
        $wp_customize->add_setting( 'custom_logo_footer_setting', array(
            'capability'        => 'edit_theme_options',
            'sanitize_callback' => 'esc_url_raw',
        ));

        $wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, 'custom_logo_footer_control', array(
            'label'    => __( 'Logo w stopce', 'textdomain' ),
            'section'  => 'title_tagline',
            'settings' => 'custom_logo_footer_setting',
            'priority' => 9,
        )));
    });
}
add_action('after_setup_theme', 'mytheme_setup');

add_filter('template_include', function ($template) {
    if (is_post_type_archive(DD_POST_TYPE)) {
        return locate_template('templates/mieszkania/archive-mieszkania.php') ?: $template;
    }

    if (is_singular(DD_POST_TYPE)) {
        return locate_template('templates/mieszkania/single-mieszkania.php') ?: $template;
    }

    return $template;
});

function add_mieszkania_rewrite_rules() {
    add_rewrite_rule(
        '^mieszkania/?$',
        'index.php?pagename=mieszkania/parter',
        'top'
    );
}
add_action('init', 'add_mieszkania_rewrite_rules');

function flush_rewrite_rules_on_activation() {
    add_mieszkania_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_rewrite_rules_on_activation');

if (is_page('mieszkania') || is_page('mieszkanie')) {
    echo '<meta name="robots" content="noindex, nofollow">';
}

class Custom_Walker_Nav_Menu extends Walker_Nav_Menu {
    public function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
        $output .= '<span class="menu-item">' . $item->title . '</span>';
    }
}

class Custom_Walker_Nav_Menu_Tel extends Walker_Nav_Menu {
    public function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
        $tel = preg_replace( '/[^0-9]/', '', $item->title );
        $output .= '<a href="tel:' . $tel . '" class="menu-item">' . esc_html( $item->title ) . '</a>';
    }
}

class Custom_Walker_Nav_Menu_Email extends Walker_Nav_Menu {
    public function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
        $email = sanitize_email( $item->title );
        if ( is_email( $email ) ) {
            $output .= '<a href="mailto:' . $email . '" class="menu-item">' . esc_html( $item->title ) . '</a>';
        } else {
            $output .= '<span class="menu-item">' . esc_html( $item->title ) . '</span>';
        }
    }
}

function add_mieszkania_shortcode_meta_box($post) {
    if ('templates/template-mieszkania.php' === get_page_template_slug()) {
        add_meta_box(
            'mieszkania_shortcode_meta_box',
            'Shortcode dla mieszkań',
            'render_mieszkania_shortcode_meta_box',
            'page',
            'normal',
            'high'
        );
    }
}
add_action('add_meta_boxes', 'add_mieszkania_shortcode_meta_box');

function render_mieszkania_shortcode_meta_box($post) {
    $shortcode = get_post_meta($post->ID, 'mieszkania_shortcode', true);
    $floor = get_post_meta($post->ID, 'mieszkania_floor', true);
    ?>
    <label for="mieszkania_shortcode">Shortcode:</label>
    <input type="text" id="mieszkania_shortcode" name="mieszkania_shortcode" value="<?php echo esc_attr($shortcode); ?>" class="widefat" />
    <br><br>
    <label for="mieszkania_floor">Numer piętra:</label>
    <input type="number" id="mieszkania_floor" name="mieszkania_floor" value="<?php echo esc_attr($floor); ?>" class="widefat" min="-1" />
    <?php
}

function save_mieszkania_shortcode_meta_box($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;
    
    if (isset($_POST['mieszkania_shortcode'])) {
        $shortcode = sanitize_text_field($_POST['mieszkania_shortcode']);
        update_post_meta($post_id, 'mieszkania_shortcode', $shortcode);
    }
    
    if (isset($_POST['mieszkania_floor'])) {
        $floor = intval($_POST['mieszkania_floor']);
        update_post_meta($post_id, 'mieszkania_floor', $floor);
    }
}
add_action('save_post', 'save_mieszkania_shortcode_meta_box');

add_action('template_redirect', function () {
    if (isset($_GET['da_image']) || is_tax('udogodnienia')) {
        status_header(404);
        nocache_headers();
        include get_template_directory() . '/404.php';
        exit;
    }
});