<?php
function load_custom_wp_media_files() {
    if (is_admin()) {
        wp_enqueue_media();
    }
}
add_action('admin_enqueue_scripts', 'load_custom_wp_media_files');

function open_media_uploader() { ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            function openUploader(buttonId, hiddenFieldId, previewId) {
                var mediaUploader;
                $(buttonId).click(function(e) {
                    e.preventDefault();
                    if (mediaUploader) {
                        mediaUploader.open();
                        return;
                    }
                    mediaUploader = wp.media({
                        title: 'Wybierz lub załaduj zdjęcie',
                        button: {
                            text: 'Wybierz to zdjęcie'
                        },
                        multiple: false
                    });
                    mediaUploader.on('select', function() {
                        var attachment = mediaUploader.state().get('selection').first().toJSON();
                        $(hiddenFieldId).val(attachment.url);
                        $(previewId).html("<img src='" + attachment.url + "' style='max-width: 150px;'/>");
                    });
                    mediaUploader.open();
                });
            }

            openUploader('#upload_vertical_button', '#vertical_shot', '#vertical_preview');
            openUploader('#upload_angled_button', '#angled_shot', '#angled_preview');

            $('#remove_vertical_shot').click(function() {
                $('#vertical_shot').val('');
                $('#vertical_preview').html('');
            });

            $('#remove_angled_shot').click(function() {
                $('#angled_shot').val('');
                $('#angled_preview').html('');
            });
        });
    </script>
<?php
}
add_action('admin_footer', 'open_media_uploader');