<?php

function extend_search_query( $query ) {
    if ( ! is_admin() && $query->is_search() && $query->is_main_query() ) {
        $search_query = $query->get('s');

        // Szukaj tylko produktów
        $query->set('post_type', array('product'));

        // Jeśli zapytanie zawiera słowo kluczowe, dodaj dodatkowe filtry
        if ($search_query) {
            $tax_query = array(
                'relation' => 'OR',
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'name',
                    'terms'    => $search_query,
                    'operator' => 'LIKE',
                ),
                array(
                    'taxonomy' => 'product_tag',
                    'field'    => 'name',
                    'terms'    => $search_query,
                    'operator' => 'LIKE',
                ),
            );
            $query->set('tax_query', $tax_query);
        }
    }
    return $query;
}
add_filter('pre_get_posts', 'extend_search_query');

function redirect_to_category_or_tag() {
    if ( ! is_admin() && is_search() && ! have_posts() && ! empty( $_GET['s'] ) ) {
        $search_query = sanitize_text_field( $_GET['s'] );

        // Sprawdź, czy istnieje kategoria z nazwą zapytania
        $category = get_term_by( 'name', $search_query, 'product_cat' );
        if ( $category ) {
            wp_redirect( get_term_link( $category ) );
            exit;
        }

        // Sprawdź, czy istnieje tag z nazwą zapytania
        $tag = get_term_by( 'name', $search_query, 'product_tag' );
        if ( $tag ) {
            wp_redirect( get_term_link( $tag ) );
            exit;
        }

        // Jeśli nie znaleziono kategorii ani tagu, ale istnieją produkty
        $products = new WP_Query( array(
            'post_type' => 'product',
            's'         => $search_query,
            'posts_per_page' => -1
        ) );
        if ( $products->have_posts() ) {
            wp_redirect( home_url( '/search/' . urlencode( $search_query ) ) );
            exit;
        }

        // Jeśli nic nie znaleziono, przekieruj na stronę główną
        wp_redirect( home_url( '/search/' . urlencode( $search_query ) ) );
        exit;
    }
}
add_action( 'template_redirect', 'redirect_to_category_or_tag' );

add_action('wp_ajax_load_more_posts', 'load_more_posts');
add_action('wp_ajax_nopriv_load_more_posts', 'load_more_posts');

function load_more_posts() {
    check_ajax_referer('my_ajax_nonce', 'nonce');

    $paged = isset($_POST['paged']) ? absint($_POST['paged']) : 1;
    $posts_per_page = 2;

    ob_start();

    $query = new WP_Query([
        'post_type'      => DD_POST_TYPE,
        'posts_per_page' => $posts_per_page,
        'paged'          => $paged,
    ]);

    if ($query->have_posts()) :
        $total_posts = $query->found_posts;
        $start_post = ($paged - 1) * $posts_per_page + 1;
        $end_post = min($paged * $posts_per_page, $total_posts);

        while ($query->have_posts()) :
            $query->the_post();

            $post_id = get_the_ID();
            $title = get_the_title();
            $id_number = get_post_meta($post_id, DD_ID, true);
            $location = get_post_meta($post_id, DD_LOCATION, true);
            $price = get_post_meta($post_id, DD_PRICE, true);
            $floor = get_post_meta($post_id, DD_FLOOR, true);
            $square = get_post_meta($post_id, DD_SQUARE, true);
            $rooms = get_post_meta($post_id, DD_ROOMS, true);
            $building_number = get_post_meta($post_id, DD_BUILDING_NUMBER, true);
            $pdf_link = get_post_meta($post_id, DD_PDF_LINK, true);
            $walkaround_3d = get_post_meta($post_id, DD_3D_LINK, true);

            $status_slug = get_post_meta($post_id, DD_STATUS, true);
            $status = isset(DD_STATUS_AVAILABILITY[$status_slug]) ? DD_STATUS_AVAILABILITY[$status_slug] : 'Niedostępny';

            $post_link = get_permalink();

            $images = get_post_meta($post_id, DD_IMAGES, true);
            $images_array = $images ? explode(',', $images) : [];
            ?>

            <div class="mieszkanie">
                <a href="<?= esc_url($post_link) ?>" class="property-link">
                    <?php if (!empty($images_array)) : ?>
                        <img src="<?= esc_url($images_array[0]) ?>" alt="<?= esc_attr($title) ?>">
                    <?php endif; ?>

                    <h3 class="title"><?= esc_html($id_number) ?></h3>

                    <div class="content">
                        <div class="top">
                            <div class="info">
                                <span class="square"><?= esc_html($square) ?> m²</span>
                                <li class="rooms"><span><?= esc_html(dd_transform_rooms($rooms)) ?></span></li>
                            </div>
                            <li class="status <?= esc_attr($status) ?>"><span><?= esc_html($status) ?></span></li>
                        </div>

                        <div class="bottom">
                            <div class="price">
                                <span class="price-label">Cena:</span>
                                <span class="price-value"><?= number_format($price, 2, ',', ' ') ?> PLN</span>
                            </div>
                        </div>
                    </div>
                </a>

                <button class="dd_add-to-clipboard" id="<?= esc_attr($post_id) ?>">
                    <svg width="20" height="20" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.22 1.41606C10.8579 1.13727 10.4113 0.990877 9.95444 1.00125C9.49758 1.01162 9.05805 1.17813 8.709 1.47306L1.709 7.47206C1.4868 7.65986 1.30824 7.89387 1.18579 8.15778C1.06333 8.42169 0.99993 8.70913 1 9.00006V18.0001C1 18.5305 1.21071 19.0392 1.58579 19.4143C1.96086 19.7894 2.46957 20.0001 3 20.0001H17C17.5304 20.0001 18.0391 19.7894 18.4142 19.4143C18.7893 19.0392 19 18.5305 19 18.0001V10.6461M13 5.00006H19M16 2.00006V8.00006" stroke="#181715" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <?php
        endwhile;

        $pagination = paginate_links([
            'total'   => $query->max_num_pages,
            'current' => $paged,
            'prev_text' => '<i class="fa fa-chevron-left"></i>',
            'next_text' => '<i class="fa fa-chevron-right"></i>',
            'type' => 'array',
        ]);

        $posts_count_message = "Wyświetlono $start_post do $end_post z $total_posts wpisów";

        wp_send_json_success(array(
            'posts' => ob_get_clean(),
            'pagination' => $pagination ? implode('', $pagination) : '',
            'posts_count' => $posts_count_message,
        ));
    else :
        wp_send_json_error(array(
            'message' => 'Brak wpisów do wyświetlenia.',
        ));
    endif;

    wp_die();
}

add_shortcode('dd_blog_posts', function ($atts = [], $content = null, $tag = ''){
    $atts = array_change_key_case( (array) $atts, CASE_LOWER );

    $dd_atts = shortcode_atts(
        array(
            'ids' => '',
        ), $atts, $tag
    );

    $postIds = explode(',', $dd_atts['ids']);

    $html = '<div class="blog-posts">';

    $args = [
        'posts_per_page' => 4,
        'post_type' => 'post',
        'post__in' => $postIds,
    ];

    $query = new WP_Query($args);

      if ($query->have_posts()){
        while ($query->have_posts()) {
          $query->the_post();
            $html .= '<div class="blog-post">
                <div class="post-thumbnail">
                    <a href="' . get_permalink() . '">';
                         if (has_post_thumbnail()) {
                            $html .= '<img src="' . get_the_post_thumbnail_url(get_the_ID(), 'medium') . '">';
                        }
                    $html .= '</a>
                </div>
                <div class="post-info">
                    <h3>
                    <a href="' . get_permalink() . '">' . get_the_title() . '</a>
                    </h3>
                    <span class="post-date">
                     ' . get_the_date() . '
                     </span>
                </div>
            </div>';
        }
        wp_reset_postdata();
        } else {
          $html .= '<p> Brak wpisów do wyświetlenia.</p >';
        }
    $html .= '</div>';

      return $html;
});