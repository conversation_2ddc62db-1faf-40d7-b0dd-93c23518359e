<?php

namespace DD\App;

class BulkWalkaroundUploadAction
{
    public static function init(): void
    {
        add_action('admin_menu', function () {
            add_submenu_page(
                'edit.php?post_type=' . DD_POST_TYPE,
                'Masowa zmiana linków 3D',
                'Masowa zmiana linków 3D',
                'manage_options',
                'dd-bulk-walkaround',
                [self::class, 'uploadPage']
            );
        });
        add_action('wp_ajax_bulk_walkaround_upload', [self::class, 'ajaxUploadAction']);
    }

    public static function uploadPage(): void
    {
        echo '<div class="wrap">';
        echo '<h1>Masowe dodawanie linków do wirtualnych spacerów</h1>';
        echo '<p>Ta strona pozwala na hurtowe dodanie linków do wirtualnych spacerów dla mieszkań.</p>';
        echo '<p>Wymagany format pliku CSV: <code>Flat,Link</code>, gdzie "Flat" to numer identyfikacyjny mieszkania, a "Link" to adres URL wirtualnego spaceru.</p>';

        echo '<div class="bulk-upload-section">';
        echo '<h2>Wybierz plik CSV</h2>';
        echo '<input type="file" id="walkaround-csv-upload" accept=".csv" />';
        echo '<button id="process-walkaround-csv" class="button button-primary">Przetwórz plik CSV</button>';

        echo '<div id="walkaround-upload-progress" style="display:none; margin-top: 10px;">';
        echo '<p>Postęp: <span id="walkaround-progress-count">0</span> / <span id="walkaround-total-count">0</span></p>';
        echo '<div class="progress-bar"><div id="walkaround-progress-bar" style="width: 0%; height: 20px; background-color: #0073aa;"></div></div>';
        echo '</div>';

        echo '<div id="walkaround-results" style="margin-top: 10px;"></div>';
        echo '</div>';
        echo '</div>';

        self::includeJavaScript();
    }

    private static function includeJavaScript(): void
    {
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                $('#process-walkaround-csv').on('click', function() {
                    var fileInput = $('#walkaround-csv-upload')[0];
                    if (!fileInput.files.length) {
                        alert('Proszę wybrać plik CSV.');
                        return;
                    }

                    var file = fileInput.files[0];
                    var reader = new FileReader();

                    reader.onload = function(e) {
                        var csvData = e.target.result;
                        processCSV(csvData);
                    };

                    reader.readAsText(file);
                });

                function processCSV(csvData) {
                    var lines = csvData.split('\n').filter(line => line.trim() !== '');

                    // Show progress UI
                    $('#walkaround-upload-progress').show();
                    $('#walkaround-total-count').text(lines.length);
                    $('#walkaround-progress-count').text(0);
                    $('#walkaround-progress-bar').css('width', '0%');
                    $('#walkaround-results').empty();

                    var processed = 0;
                    var results = {
                        success: 0,
                        failed: 0,
                        skipped: 0,
                        details: []
                    };

                    function processNextLine(index) {
                        if (index >= lines.length) {
                            // All lines processed
                            showResults(results);
                            return;
                        }

                        var line = lines[index].trim();
                        var parts = line.split(',');

                        if (parts.length !== 2) {
                            results.failed++;
                            results.details.push({
                                line: line,
                                status: 'failed',
                                message: 'Nieprawidłowy format linii CSV'
                            });
                            updateProgress(++processed, lines.length);
                            processNextLine(index + 1);
                            return;
                        }

                        var propertyId = parts[0].trim();
                        var walkaroundLink = parts[1].trim();

                        // Send to server for processing
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'bulk_walkaround_upload',
                                nonce: '<?php echo wp_create_nonce('bulk_walkaround_upload_nonce'); ?>',
                                property_id: propertyId,
                                walkaround_link: walkaroundLink
                            },
                            success: function(response) {
                                if (response.success) {
                                    results.success++;
                                    results.details.push({
                                        line: line,
                                        status: 'success',
                                        message: response.data.message,
                                        post_title: response.data.post_title
                                    });
                                } else {
                                    if (response.data.status === 'skipped') {
                                        results.skipped++;
                                    } else {
                                        results.failed++;
                                    }
                                    results.details.push({
                                        line: line,
                                        status: response.data.status,
                                        message: response.data.message
                                    });
                                }
                                updateProgress(++processed, lines.length);
                                processNextLine(index + 1);
                            },
                            error: function() {
                                results.failed++;
                                results.details.push({
                                    line: line,
                                    status: 'failed',
                                    message: 'Błąd serwera podczas przetwarzania linii'
                                });
                                updateProgress(++processed, lines.length);
                                processNextLine(index + 1);
                            }
                        });
                    }

                    // Start processing
                    processNextLine(0);
                }

                function updateProgress(current, total) {
                    var percentage = Math.round((current / total) * 100);
                    $('#walkaround-progress-count').text(current);
                    $('#walkaround-progress-bar').css('width', percentage + '%');
                }

                function showResults(results) {
                    var $results = $('#walkaround-results');

                    $results.append('<h3>Wyniki:</h3>');
                    $results.append('<p>Pomyślnie przetworzono: ' + results.success + '</p>');
                    $results.append('<p>Pominięto: ' + results.skipped + '</p>');
                    $results.append('<p>Nie udało się: ' + results.failed + '</p>');

                    if (results.details.length > 0) {
                        var $table = $('<table class="widefat striped">');
                        $table.append('<thead><tr><th>Linia CSV</th><th>Status</th><th>Mieszkanie</th><th>Informacja</th></tr></thead>');
                        var $tbody = $('<tbody>');

                        $.each(results.details, function(i, detail) {
                            var statusClass = '';
                            var statusText = '';

                            if (detail.status === 'success') {
                                statusClass = 'success';
                                statusText = 'Sukces';
                            } else if (detail.status === 'skipped') {
                                statusClass = 'skipped';
                                statusText = 'Pominięto';
                            } else {
                                statusClass = 'failed';
                                statusText = 'Błąd';
                            }

                            var $row = $('<tr>');
                            $row.append('<td>' + detail.line + '</td>');
                            $row.append('<td class="' + statusClass + '">' + statusText + '</td>');
                            $row.append('<td>' + (detail.post_title || '-') + '</td>');
                            $row.append('<td>' + detail.message + '</td>');

                            $tbody.append($row);
                        });

                        $table.append($tbody);
                        $results.append($table);
                    }

                    $results.append('<style>.success{color:green;}.failed{color:red;}.skipped{color:orange;}.progress-bar{width:100%;background-color:#eee;border-radius:3px;overflow:hidden;}</style>');
                }
            });
        </script>
        <?php
    }

    public static function ajaxUploadAction(): void
    {
        check_ajax_referer('bulk_walkaround_upload_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Brak uprawnień', 'status' => 'failed'], 403);
        }

        $property_id = isset($_POST['property_id']) ? sanitize_text_field($_POST['property_id']) : '';
        $walkaround_link = isset($_POST['walkaround_link']) ? esc_url_raw($_POST['walkaround_link']) : '';

        if (empty($property_id) || empty($walkaround_link)) {
            wp_send_json_error([
                'message' => 'Nieprawidłowe dane wejściowe',
                'status' => 'failed'
            ]);
        }

        // Find post with matching ID
        $posts = get_posts([
            'post_type' => DD_POST_TYPE,
            'posts_per_page' => 1,
            'meta_query' => [
                [
                    'key' => DD_ID,
                    'value' => $property_id,
                    'compare' => '='
                ],
                [
                    'key' => DD_TYPE,
                    'value' => DD_TYPE_MIESZKANIE,
                    'compare' => '='
                ]
            ]
        ]);

        if (empty($posts)) {
            wp_send_json_error([
                'message' => 'Nie znaleziono mieszkania o ID: ' . $property_id,
                'status' => 'failed'
            ]);
        }

        $post = $posts[0];
        $post_id = $post->ID;

        // Update post meta for 3D link
        update_post_meta($post_id, DD_3D_LINK, $walkaround_link);

        wp_send_json_success([
            'message' => 'Pomyślnie dodano link do wirtualnego spaceru',
            'post_title' => $post->post_title
        ]);
    }
}
