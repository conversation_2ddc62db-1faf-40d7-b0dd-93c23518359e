<?php

function draw_mieszkania($floor) {
    $api_url = esc_url(rest_url("dd/v2/realestate/?floor={$floor}"));
    ?>
    <script>
        document.addEventListener("DOMContentLoaded", async function () {
            const apiUrl = "<?php echo $api_url; ?>";
            let floor = new Map();

            try {
                const response = await fetch(apiUrl);
                if (!response.ok) throw new Error("Błąd pobierania danych");
                const mieszkania = await response.json();

                if (!mieszkania.length) return;

                if (!mieszkania.every(m => m.id_number)) {
                    throw new Error("Brak wymaganych danych w odpowiedzi");
                }

                mieszkania.forEach(m => floor.set(m.id_number, m));

                draw_mieszkania_status(floor);
                draw_mieszkania_tooltip(floor);

            } catch (error) {
                console.error("Błąd ładowania danych miesz<PERSON>:", error);
            }
        });

        function draw_mieszkania_status(floor) {
            var hotspots = {};
            var allHotspots = document.querySelectorAll("path.leaflet-interactive");

            if (!allHotspots.length) {
                console.warn("Brak hotspotów do przetworzenia.");
                return;
            }

            allHotspots.forEach(function(hotspot) {
                hotspots[hotspot.getAttribute("name")] = hotspot;
            });

            floor.forEach(function(mieszkanie) {
                const hotspot = hotspots[mieszkanie.id_number];
                const statusClass = mieszkanie.status ? `hotspot-${mieszkanie.status.toLowerCase()}` : 'hotspot-default';

                if (hotspot) {
                    hotspot.classList.remove('hotspot-default');
                    if (statusClass) {
                        hotspot.classList.add(statusClass);
                    }
                }
            });
        }

        function draw_mieszkania_tooltip(floor) {
            const pane = document.querySelector(".leaflet-pane");
            if (pane) {
                pane.addEventListener("click", (event) => handleHotspotClick(event, floor));
            }

            function handleHotspotClick(event, floor) {
                const hotspot = event.target.closest("path.leaflet-interactive");
                if (!hotspot) return;

                const hotspotId = hotspot.getAttribute("name");
                const realEstate = floor.get(hotspotId);

                if (!realEstate) return;

                const tooltipTitleObserver = new MutationObserver(() => {
                    const tooltipTitle = document.querySelector(".leaflet-rrose-content-wrapper h2.hotspot-title");
                    if (tooltipTitle && tooltipTitle.innerText === hotspotId) {
                        tooltipTitle.closest(".leaflet-rrose-content").innerHTML = generateTooltipContent(realEstate);
                        tooltipTitleObserver.disconnect();
                    }
                });

                tooltipTitleObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }

            function generateTooltipContent(realEstate) {
                return `
                    <div class='mieszkanie-tooltip tooltip-hotspot-${realEstate.id_number}'>
                        <div class='row ${realEstate.status.toLowerCase()}'>
                            <span class='title'>${realEstate.property_type}</span>
                            <strong>${realEstate.id_number}</strong>
                        </div>
                        ${realEstate.price > 0 ? `<div class='row'><span>Cena: </span><strong>${parseFloat(realEstate.price).toLocaleString('pl-PL')} zł</strong></div>` : ""}
                        ${realEstate.pricemkw > 0 ? `<div class='row'><span>Cena m²: </span><strong>${parseFloat(realEstate.pricemkw).toLocaleString('pl-PL')} zł</strong></div>` : ""}
                        ${realEstate.size > 0 ? `<div class='row'><span>Metraż: </span><strong>${realEstate.size} m²</strong></div>` : ""}
                        ${realEstate.rooms > 0 ? `<div class='row'><span>Liczba pokoi: </span><strong>${realEstate.rooms}</strong></div>` : ""}
                        ${realEstate.facilities?.length ? `<div class='row'><div class="flex"><span>Udogodnienia: </span></div><ul class="list">${realEstate.facilities.map(f => `<li>${f.name}</li>`).join("")}</ul></div>` : ""}
                        <a href='${realEstate.url}' class='button'>
                            Szczegóły
                            <img src='<?php echo esc_url(DD_PLUGIN_ASSETS . "images/small-arrow-right-white.svg"); ?>' alt='Przejdź do podstrony' class='arrow'>
                        </a>
                    </div>
                `;
            }
        }
    </script>
    <?php
}
?>
