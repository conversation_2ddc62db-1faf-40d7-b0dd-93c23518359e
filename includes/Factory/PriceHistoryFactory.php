<?php

namespace DD\App\Factory;

use DateTime;
use DD\App\DTO\PriceHistoryDto;
use DD\App\Logger;

class PriceHistoryFactory
{
    /**
     * @return array<PriceHistoryDto>
     */
    public function createPriceHistoryFromJson(?string $json): array
    {
        if ($json === null) {
            return [];
        }

        $decoded = json_decode($json, true);

        if (!is_array($decoded)) {
            return [];
        }

        $priceHistory = [];

        foreach ($decoded as $rawPriceHistory) {
            $date = DateTime::createFromFormat('Y-m-d H:i:s.u', $rawPriceHistory['modifiedDate']['date']);

            if ($date === false) {
                Logger::error('Failed to read price history date saved in DB: ' . $rawPriceHistory['modifiedDate']['date']);

                continue;
            }

            $priceHistory[] = new PriceHistoryDto(
                $date,
                $rawPriceHistory['beforePrice'],
                $rawPriceHistory['beforePerMeterPrice'],
                $rawPriceHistory['afterPrice'],
                $rawPriceHistory['afterPerMeterPrice']
            );
        }

        return $priceHistory;
    }
}