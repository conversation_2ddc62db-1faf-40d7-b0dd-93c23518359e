<?php

namespace DD\App\Factory;

use DD\App\DTO\PriceHistoryDto;
use DD\App\DTO\RealEstate;
use DD\App\Enum\Status;
use DD\App\Enum\Type;
use Exception;

class RealEstateFromXmlFactory
{
    /**
     * @param array{
     *       id: string|int,
     *       local_number?: string,
     *       status_id?: string|int,
     *       area?: string|float,
     *       floor?: string|int,
     *       price?: string|float,
     *       pricemkw?: string|float,
     *       type_id?: string|int,
     *       balkon?: string|float,
     *       card_link?: string,
     *       rooms?: string|int,
     *   } $rawDataFromXml
     * @param PriceHistoryDto[] $priceHistory
     *
     * @throws Exception
     */
    public function createRealEstate(array $rawDataFromXml, array $priceHistory = []): RealEstate
    {
        $floor = isset($rawDataFromXml['floor']) ? (int) $rawDataFromXml['floor'] : null;
        $price = isset($rawDataFromXml['price']) ? (float) $rawDataFromXml['price'] : 0.0;
        $pricePerMeter = isset($rawDataFromXml['pricemkw']) ? (float) $rawDataFromXml['pricemkw'] : 0.0;
        $area = is_numeric($rawDataFromXml['area']) ? (float) $rawDataFromXml['area'] : null;
        $rooms = is_numeric($rawDataFromXml['rooms']) ? (int) $rawDataFromXml['rooms'] : null;
        $status = match ((int) $rawDataFromXml['status_id']) {
            1 => Status::Dostepne,
            2, 3 => Status::Rezerwacja,
            default => Status::Sprzedane,
        };
        $type = match ((int) $rawDataFromXml['type_id']) {
            1 => Type::Mieszkanie,
            2, 3, 5, 12, 13, 14, 15, 26 => Type::Garaz,
            11 => Type::ParkingZewnatrz,
            4, 6, 7, 10 => Type::KomorkaLokatorska,
            8, 9 => Type::LokalUslugowy,
            default => throw new Exception("Type {$rawDataFromXml['type_id']} not found"),
        };
        $balcony = mb_strlen((string) ($rawDataFromXml['balkon'] ?? '')) > 0;

        return new RealEstate(
            externalId: (int) $rawDataFromXml['id'],
            localNumber: $rawDataFromXml['local_number'],
            floor: $floor,
            type: $type,
            status: $status,
            price: $price,
            pricePerMeter: $pricePerMeter,
            hasBalcony: $balcony,
            cardLinkPdf: $rawDataFromXml['card_link'] ?? null,
            area: $area,
            rooms: $rooms,
            priceHistory: $priceHistory,
        );
    }
}