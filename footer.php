<footer>
    <div class="footer-container">
        <div class="container">
            <div class="left-right">
                <div class="info left-right">
                    <div class="left side">
                        <?php
                        $custom_logo_footer_url = get_theme_mod( 'custom_logo_footer_setting' );
                        ?>
                        <?php if ( ! empty( $custom_logo_footer_url ) ) : ?>
                        <a href="/" class="logo-container">
                            <img src="<?php echo esc_url( $custom_logo_footer_url ); ?>"
                                alt="Logo <?php bloginfo( 'name' ); ?>">
                        </a>
                        <?php endif; ?>
                        <p>Jesteśmy 30 lat na rynku sprzedaży mieszkań, wyznaczamy trendy. Nasza główna reklama to
                            opinie zadowolonych klientów. Kierujemy się: jakością, uczciwością i zaufaniem.</p>
                        <div class="bottom">
                            <a href="https://kuncer.pl/">Kuncer.pl</a>
                            <a href="https://www.facebook.com/pbkuncer/" class="social-links" target="_blank" rel="noopener noreferrer">
								<img src="<?php echo get_template_directory_uri(); ?>/public/facebook.svg" alt="Facebook">
							</a>
							<a href="https://www.instagram.com/p.b.kuncer/" class="social-links" target="_blank" rel="noopener noreferrer">
								<img src="<?php echo get_template_directory_uri(); ?>/public/instagram.svg" alt="Instagram">
							</a>
                        </div>
                    </div>
                </div>

                <div class="menus">
                    <?php
                    $locations = get_nav_menu_locations();
                    if ( isset( $locations['footer-menu-1'] ) ) {
                        $menu_id = $locations['footer-menu-1'];
                        $footer_menu = wp_get_nav_menu_object( $menu_id );
                        $menu_title = $footer_menu ? $footer_menu->name : '';

                        if ( ! empty( $menu_title ) ) {
                            ?>
                            <div class="footer-menu menu" role="navigation">
                                <span class="menu-title"><?php echo $menu_title; ?></span>
                                <?php
                                wp_nav_menu(
                                    array(
                                        'theme_location' => 'footer-menu-1',
                                        'menu_id' => 'footer-menu-1',
                                        'container' => 'ul',
                                        'menu_class' => 'footer-menu-1',
                                        'depth' => "1",
                                        'walker' => new Custom_Walker_Nav_Menu()
                                    )
                                );
                                ?>
                            </div>
                            <?php
                        }
                    } ?>

					<div class="flex">
						<div class="footer-menu menu" role="navigation">
							<img src="<?php echo get_template_directory_uri(); ?>/public/footer-location.svg"alt="Adres">
							<div class="content">
								<span class="menu-title"><?php echo $menu_title; ?></span>
								<p>PRZEDSIĘBIORSTWO BUDOWLANE KUNCER SP. Z O.O.</p>
								<p>UL. WĄWOZOWA 15</p>
								<p>75-339 KOSZALIN</p>
							</div>
						</div>
						
					  <?php if ( isset( $locations['footer-menu-3'] ) ) {
							$menu_id = $locations['footer-menu-3'];
							$footer_menu = wp_get_nav_menu_object( $menu_id );
							$menu_title = $footer_menu ? $footer_menu->name : '';

							if ( ! empty( $menu_title ) ) {
								?>
								<div class="footer-menu menu" role="navigation">
								<img src="<?php echo get_template_directory_uri(); ?>/public/footer-phone.svg"alt="Telefon">
									<div class="content">
									<span class="menu-title"><?php echo $menu_title; ?></span>
										<?php
										wp_nav_menu(
											array(
												'theme_location' => 'footer-menu-3',
												'menu_id' => 'footer-menu-3',
												'container' => 'ul',
												'menu_class' => 'footer-menu-3',
												'depth' => "1",
												'walker' => new Custom_Walker_Nav_Menu_Tel()
											)
										);
										?>
									</div>
								</div>
								<?php
							}
						}

						if ( isset( $locations['footer-menu-4'] ) ) {
							$menu_id = $locations['footer-menu-4'];
							$footer_menu = wp_get_nav_menu_object( $menu_id );
							$menu_title = $footer_menu ? $footer_menu->name : '';

							if ( ! empty( $menu_title ) ) {
								?>
								<div class="footer-menu menu" role="navigation">
								<img src="<?php echo get_template_directory_uri(); ?>/public/footer-email.svg"alt="E-mail">
									<div class="content">
									<span class="menu-title"><?php echo $menu_title; ?></span>
										<?php
										wp_nav_menu(
											array(
												'theme_location' => 'footer-menu-4',
												'menu_id' => 'footer-menu-4',
												'container' => 'ul',
												'menu_class' => 'footer-menu-4',
												'depth' => "1",
												'walker' => new Custom_Walker_Nav_Menu_Email()
											)
										);
										?>
									</div>
								</div>
								<?php
							}
						}
						?>
					</div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-menu menu" role="navigation">
                    <p>Copyright &copy; <?php echo date('Y'); ?> P.B. KUNCER</p>
                    <ul id="footer-menu-5" class="footer-menu-5">
                        <?php
                        if ( isset( $locations['footer-menu-5'] ) ) {
                            $menu_id = $locations['footer-menu-5'];
                            $footer_menu = wp_get_nav_menu_object( $menu_id );
                            $menu_items = wp_get_nav_menu_items( $menu_id );

                            if ( ! empty( $menu_items ) ) {
                                foreach ( $menu_items as $item ) {
                                    ?>
                                    <li class="menu-item">
                                        <a href="<?php echo esc_url( $item->url ); ?>" class="menu-title"><?php echo esc_html( $item->title ); ?></a>
                                    </li>
                                    <?php
                                }
                            }
                        }
                        ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</footer>
<?php wp_footer(); ?>
</body>
</html>
