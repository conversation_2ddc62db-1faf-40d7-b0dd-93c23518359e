version: '3.3'

services:
    mariadb:
        image: mariadb:10.3.6
        container_name: "mariadb-${SERVICE_NAME}"
        volumes:
            - "./db:/var/lib/mysql"
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: somewordpress
            MYSQL_DATABASE: wordpress
            MYSQL_USER: wordpress
            MYSQL_PASSWORD: wordpress
        ports:
            - "${EXPOSE_PORT_DB}:3306"

    wordpress:
        container_name: "wordpress-${SERVICE_NAME}"
        depends_on:
            - mariadb
        image: wordpress:${WP_VERSION}-php${PHP_VERSION}-apache
        ports:
            - "${EXPOSE_PORT}:80"
        restart: always
        environment:
            WORDPRESS_DB_HOST: "mariadb-${SERVICE_NAME}:3306"
            WORDPRESS_DB_USER: wordpress
            WORDPRESS_DB_PASSWORD: wordpress
            WORDPRESS_DEBUG: 1
            MAILHOG_HOST: "mailhog-${SERVICE_NAME}"
        volumes:
            - ./wordpress:/var/www/html
            - "../:/var/www/html/wp-content/plugins/${SERVICE_NAME}"
            - "./smtp-local:/var/www/html/wp-content/plugins/smtp-local"

    mailhog:
        container_name: "mailhog-${SERVICE_NAME}"
        image: mailhog/mailhog
        ports:
            - "${EXPOSE_PORT_MAILHOG}:8025"
        restart: always
volumes:
    mariadb:
