<?php

namespace DD\Tests;

use DateTime;
use DD\App\DTO\PriceHistoryDto;
use DD\App\XmlImporter\FileReader;
use DD\App\XmlImporter\PriceHistoryImporter;
use DD\App\XmlImporter\XmlFileToArrayConverter;
use Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PriceHistoryImporterTest extends TestCase
{

    private MockObject $fileReader;
    private PriceHistoryImporter $serviceUnderTest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->fileReader = $this->createMock(FileReader::class);
        $this->serviceUnderTest = new PriceHistoryImporter($this->fileReader);
    }

    public function test_import_when_there_is_no_history(): void
    {
        /** @Given */
        $apiUrl = 'test.xml';
        $xmlContent = <<<XML
        <xml>
            <realestate>
                <id>2469</id>
                <investment_id>5</investment_id>
                <investment_name>Hallera bud.3</investment_name>
                <investment_stage/>
                <building/>
                <name>A1</name>
                <local_number>A1</local_number>
                <type_id>1</type_id>
                <type>Mieszkanie</type>
                <price>346940.00</price>
                <price_net>321240.74</price_net>
                <pricemkw>8300.00</pricemkw>
                <pricemkw_net>7685.19</pricemkw_net>
                <price_history/>
            </realestate>
        </xml>
        XML;

        $id = 2469;

        $this->fileReader->expects($this->once())
            ->method('readFile')
            ->with($apiUrl)
            ->willReturn($xmlContent);

        /** @When */
        $actual = $this->serviceUnderTest->getPriceHistory($id, $apiUrl);

        /** @Then */
        $this->assertEquals([], $actual);
    }

    public function test_import_with_incorrect_id(): void
    {
        /** @Given */
        $apiUrl = 'test.xml';
        $xmlContent = <<<XML
        <xml>
            <realestate>
                <id>2469</id>
                <investment_id>5</investment_id>
                <investment_name>Hallera bud.3</investment_name>
                <investment_stage/>
                <building/>
                <name>A1</name>
                <local_number>A1</local_number>
                <type_id>1</type_id>
                <type>Mieszkanie</type>
                <price>346940.00</price>
                <price_net>321240.74</price_net>
                <pricemkw>8300.00</pricemkw>
                <pricemkw_net>7685.19</pricemkw_net>
                <price_history/>
            </realestate>
        </xml>
        XML;

        $id = 987654321;

        $this->fileReader->expects($this->once())
            ->method('readFile')
            ->with($apiUrl)
            ->willReturn($xmlContent);

        /** @Then */
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('ID is not matching');

        /** @When */
        $this->serviceUnderTest->getPriceHistory($id, $apiUrl);
    }

    public function test_import_with_full_history(): void
    {
        /** @Given */
        $apiUrl = 'test.xml';
        $xmlContent = <<<XML
        <xml>
          <realestate>
            <id>1</id>
            <investment_id>1</investment_id>
            <investment_name>Testowa Inwestycja</investment_name>
            <investment_stage/>
            <building>A</building>
            <name>A - M1</name>
            <local_number>M1</local_number>
            <type_id>1</type_id>
            <type>Mieszkanie</type>
            <price>490000.00</price>
            <price_net>453703.70</price_net>
            <pricemkw>10888.89</pricemkw>
            <pricemkw_net>10082.31</pricemkw_net>
            <price_history>
              <price_change>
                <date_modified>2025-05-26 17:00:00</date_modified>
                <before>
                  <price>500000.00</price>
                  <price_net>462962.96</price_net>
                  <pricemkw>11111.11</pricemkw>
                  <pricemkw_net>10288.06</pricemkw_net>
                </before>
                <after>
                  <price>490000.00</price>
                  <price_net>453703.70</price_net>
                  <pricemkw>10888.89</pricemkw>
                  <pricemkw_net>10082.31</pricemkw_net>
                </after>
              </price_change>
              <price_change>
                <date_modified>2025-04-12 13:54:33</date_modified>
                <before>
                  <price>510000.00</price>
                  <price_net>472222.22</price_net>
                  <pricemkw>11333.33</pricemkw>
                  <pricemkw_net>10493.82</pricemkw_net>
                </before>
                <after>
                  <price>500000.00</price>
                  <price_net>462962.96</price_net>
                  <pricemkw>11111.11</pricemkw>
                  <pricemkw_net>10288.06</pricemkw_net>
                </after>
              </price_change>
            </price_history>
          </realestate>
        </xml>
        XML;

        $id = 1;

        $expected = [
            new PriceHistoryDto(
                new DateTime('2025-05-26 17:00:00'),
                500000.00,
                11111.11,
                490000.00,
                10888.89
            ),
            new PriceHistoryDto(
                new DateTime('2025-04-12 13:54:33'),
                510000.00,
                11333.33,
                500000.00,
                11111.11
            ),
        ];

        $this->fileReader->expects($this->once())
            ->method('readFile')
            ->with($apiUrl)
            ->willReturn($xmlContent);

        /** @When */
        $actual = $this->serviceUnderTest->getPriceHistory($id, $apiUrl);

        /** @Then */
        $this->assertEquals($expected, $actual);
    }

    public function test_import_with_single_item(): void
    {
        /** @Given */
        $apiUrl = 'test.xml';
        $xmlContent = <<<XML
        <xml>
          <realestate>
            <id>1</id>
            <investment_id>1</investment_id>
            <investment_name>Testowa Inwestycja</investment_name>
            <investment_stage/>
            <building>A</building>
            <name>A - M1</name>
            <local_number>M1</local_number>
            <type_id>1</type_id>
            <type>Mieszkanie</type>
            <price>490000.00</price>
            <price_net>453703.70</price_net>
            <pricemkw>10888.89</pricemkw>
            <pricemkw_net>10082.31</pricemkw_net>
            <price_history>
              <price_change>
                <date_modified>2025-05-26 17:00:00</date_modified>
                <before>
                  <price>500000.00</price>
                  <price_net>462962.96</price_net>
                  <pricemkw>11111.11</pricemkw>
                  <pricemkw_net>10288.06</pricemkw_net>
                </before>
                <after>
                  <price>490000.00</price>
                  <price_net>453703.70</price_net>
                  <pricemkw>10888.89</pricemkw>
                  <pricemkw_net>10082.31</pricemkw_net>
                </after>
              </price_change>
            </price_history>
          </realestate>
        </xml>
        XML;

        $id = 1;

        $expected = [
            new PriceHistoryDto(
                new DateTime('2025-05-26 17:00:00'),
                500000.00,
                11111.11,
                490000.00,
                10888.89
            ),
        ];

        $this->fileReader->expects($this->once())
            ->method('readFile')
            ->with($apiUrl)
            ->willReturn($xmlContent);

        /** @When */
        $actual = $this->serviceUnderTest->getPriceHistory($id, $apiUrl);

        /** @Then */
        $this->assertEquals($expected, $actual);
    }
}