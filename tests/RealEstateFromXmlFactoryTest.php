<?php

namespace DD\Tests;

use DateTime;
use DD\App\DTO\PriceHistoryDto;
use DD\App\DTO\RealEstate;
use DD\App\Enum\Status;
use DD\App\Enum\Type;
use DD\App\Factory\RealEstateFromXmlFactory;
use Generator;
use PHPUnit\Framework\TestCase;

class RealEstateFromXmlFactoryTest extends TestCase
{
    private RealEstateFromXmlFactory $serviceUnderTest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->serviceUnderTest = new RealEstateFromXmlFactory();
    }

    public function test_minimum_data(): void
    {
        /** @Given */
        $rawData = [
            'id' => '1234',
            'local_number' => 'A41',
            'status_id' => '1',
            'floor' => '2',
            'price' => '123000',
            'pricemkw' => '12300',
            'type_id' => '1',
            'area' => '123',
            'rooms' => 3,
        ];

        $expected = new RealEstate(
            1234,
            'A41',
            2,
            Type::Mi<PERSON>zkanie,
            Status::Dostepne,
            123000,
            12300,
            area: 123,
            rooms: 3,
        );

        /** @When */
        $actual = $this->serviceUnderTest->createRealEstate($rawData);

        /** @Then */
        self::assertEquals($expected, $actual);
    }

    public function test_full_data(): void
    {
        /** @Given */
        $rawData = [
            'id' => '1234',
            'local_number' => 'A41',
            'status_id' => '1',
            'floor' => '2',
            'price' => '123000',
            'pricemkw' => '12300',
            'type_id' => '1',
            'balkon' => '3.2+1.2',
            'area' => '32.12',
            'card_link' => 'https://pdf.pl',
            'rooms' => '4'
        ];

        $priceHistory = [
            new PriceHistoryDto(
                new DateTime('2025-05-26 17:00:00'),
                500000.00,
                11111.11,
                490000.00,
                10888.89
            ),
            new PriceHistoryDto(
                new DateTime('2025-04-12 13:54:33'),
                510000.00,
                11333.33,
                500000.00,
                11111.11
            ),
        ];

        $expected = new RealEstate(
            1234,
            'A41',
            2,
            Type::Mieszkanie,
            Status::Dostepne,
            123000,
            12300,
            true,
            'https://pdf.pl',
            32.12,
            4,
            priceHistory: $priceHistory
        );

        /** @When */
        $actual = $this->serviceUnderTest->createRealEstate($rawData, $priceHistory);

        /** @Then */
        self::assertEquals($expected, $actual);
    }

    public static function different_statuses_data_provider(): Generator
    {
        yield 'Dostępne' => ['1', Status::Dostepne];
        yield 'Rezerwacja ustna' => ['2', Status::Rezerwacja];
        yield 'Umowa rezerwacyjna' => ['3', Status::Rezerwacja];
        yield 'Sprzedane' => ['4', Status::Sprzedane];
        yield 'Umowa przedwstępna' => ['5', Status::Sprzedane];
        yield 'Umowa deweloperska' => ['6', Status::Sprzedane];
        yield 'Akt notarialny' => ['7', Status::Sprzedane];
        yield 'Odbiór' => ['8', Status::Sprzedane];
        yield 'Wstrzymany ' => ['9', Status::Sprzedane];
    }

    /**
     * @dataProvider different_statuses_data_provider()
     */
    public function test_different_statuses(string $rawStatus, Status $expectedStatus): void
    {
        /** @Given */
        $rawData = [
            'id' => '1234',
            'local_number' => 'A41',
            'status_id' => $rawStatus,
            'floor' => '2',
            'price' => '123000',
            'pricemkw' => '12300',
            'type_id' => '1',
            'area' => '123',
            'rooms' => 3,
        ];

        $expected = new RealEstate(
            1234,
            'A41',
            2,
            Type::Mieszkanie,
            $expectedStatus,
            123000,
            12300,
            area: 123,
            rooms: 3,
        );

        /** @When */
        $actual = $this->serviceUnderTest->createRealEstate($rawData);

        /** @Then */
        self::assertEquals($expected, $actual);
    }

    public static function different_types_data_provider(): Generator
    {
        yield 'Mieszkanie' => ['1', Type::Mieszkanie];
        yield 'Garaz' => ['2', Type::Garaz];
        yield 'Miejsce postojowe' => ['3', Type::Garaz];
        yield 'Komórka lokatorska' => ['4', Type::KomorkaLokatorska];
        yield 'Miejsce postojowe zadaszone' => ['5', Type::Garaz];
        yield 'Piwnica' => ['6', Type::KomorkaLokatorska];
        yield 'Boks rowerowy' => ['7', Type::KomorkaLokatorska];
        yield 'Lokal Komercyjny' => ['8', Type::LokalUslugowy];
        yield 'Lokal Usługowy' => ['9', Type::LokalUslugowy];
        yield 'Schowek' => ['10', Type::KomorkaLokatorska];
        yield 'Miejsce postojowe naziemne' => ['11', Type::ParkingZewnatrz];
        yield 'Miejsce postojowe podziemne' => ['12', Type::Garaz];
        yield 'Miejsce postojowe ze schowkiem' => ['13', Type::Garaz];
        yield 'Miejsce postojowe inwalidzkie' => ['26', Type::Garaz];
    }

    /**
     * @dataProvider different_types_data_provider()
     */
    public function test_different_types(string $rawType, Type $expectedType): void
    {
        /** @Given */
        $rawData = [
            'id' => '1234',
            'local_number' => 'A41',
            'status_id' => '1',
            'floor' => '2',
            'price' => '123000',
            'pricemkw' => '12300',
            'type_id' => $rawType,
            'area' => 123,
            'rooms' => 3,
        ];

        $expected = new RealEstate(
            1234,
            'A41',
            2,
            $expectedType,
            Status::Dostepne,
            123000,
            12300,
            area: 123,
            rooms: 3,
        );

        /** @When */
        $actual = $this->serviceUnderTest->createRealEstate($rawData);

        /** @Then */
        self::assertEquals($expected, $actual);
    }
}