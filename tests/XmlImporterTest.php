<?php

namespace DD\Tests;

use DD\App\XmlImporter\XmlFileToArrayConverter;
use Generator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class XmlImporterTest extends TestCase
{
    private XmlFileToArrayConverter $serviceUnderTest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->serviceUnderTest = new XmlFileToArrayConverter();
    }

    public static function xml_string_to_array_data_provider(): Generator
    {
        yield [
            'xmlContent' => '
            <xml>
                <realestate>
                <id>2469</id>
                <name>A1</name>
                <local_number>A1</local_number>
                <area>70.01</area>
                </realestate>
            </xml>',
            'expectedArray' => [
                [
                    'id' => 2469,
                    'name' => 'A1',
                    'local_number' => 'A1',
                    'area' => 70.01,
                ]
            ],
        ];

        yield [
            'xmlContent' => '
            <xml>
                <realestate>
                    <test_atr>ads</test_atr>
                </realestate>
            </xml>',
            'expectedArray' => [
                [
                    'test_atr' => 'ads',
                ]
            ],
        ];

        yield [
            'xmlContent' => '
            <xml>
                <realestate>
                    <test_atr>ads</test_atr>
                </realestate>
                <realestate>
                    <test_atr>gsd</test_atr>
                </realestate>
            </xml>',
            'expectedArray' => [
                [
                    'test_atr' => 'ads',
                ],
                [
                    'test_atr' => 'gsd',
                ]
            ],
        ];
    }

    #[DataProvider('xml_string_to_array_data_provider')]
    public function test_converting_xml_string_to_array(string $xmlContent, array $expectedArray): void
    {
        /** @When */
        $actual = $this->serviceUnderTest->convertStringXmlToArray($xmlContent);

        /** @Then */
        $this->assertEqualsCanonicalizing($expectedArray, $actual);
    }

    public function test_incorrect_structure_of_xml(): void
    {
        /** @Given */
        $xmlContent = <<<XML
        <xml>
            <flat>
                <id>123</id>
            </flat>
        </xml>
        XML;

        /** @Then */
        $this->expectExceptionMessage('XML structure is incorrect');

        /** @When */
        $actual = $this->serviceUnderTest->convertStringXmlToArray($xmlContent);
    }
}