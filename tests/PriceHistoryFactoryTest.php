<?php

namespace DD\Tests;

use DateTime;
use DD\App\DTO\PriceHistoryDto;
use DD\App\Factory\PriceHistoryFactory;
use PHPUnit\Framework\TestCase;

class PriceHistoryFactoryTest extends TestCase
{
    private PriceHistoryFactory $serviceUnderTest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->serviceUnderTest = new PriceHistoryFactory();
    }

    public function test_empty_price_history(): void
    {
        /** @Given */
        $rawData = '[]';

        /** @When */
        $actual = $this->serviceUnderTest->createPriceHistoryFromJson($rawData);

        /** @Then */
        $this->assertEquals([], $actual);
    }

    public function test_full_price_history(): void
    {
        /** @Given */
        $rawData = '[{"modifiedDate":{"date":"2023-12-22 11:44:47.000000","timezone_type":3,"timezone":"UTC"},"beforePrice":285128.4,"beforePerMeterPrice":4752.14,"afterPrice":370666.92,"afterPerMeterPrice":6177.78},{"modifiedDate":{"date":"2025-01-07 14:05:59.000000","timezone_type":3,"timezone":"UTC"},"beforePrice":370666.92,"beforePerMeterPrice":6177.78,"afterPrice":360666.92,"afterPerMeterPrice":6011.12}]';

        $expected = [
            new PriceHistoryDto(
                new DateTime('2023-12-22 11:44:47'),
                285128.4,
                4752.14,
                370666.92,
                6177.78
            ),
            new PriceHistoryDto(
                new DateTime('2025-01-07 14:05:59'),
                370666.92,
                6177.78,
                360666.92,
                6011.12
            ),
        ];

        /** @When */
        $actual = $this->serviceUnderTest->createPriceHistoryFromJson($rawData);

        /** @Then */
        $this->assertEquals($expected, $actual);
    }

    public function test_create_with_one_invalid_date(): void
    {
        /** @Given */
        $rawData = '[{"modifiedDate":{"date":"dupa maryny","timezone_type":3,"timezone":"UTC"},"beforePrice":285128.4,"beforePerMeterPrice":4752.14,"afterPrice":370666.92,"afterPerMeterPrice":6177.78},{"modifiedDate":{"date":"2025-01-07 14:05:59.000000","timezone_type":3,"timezone":"UTC"},"beforePrice":370666.92,"beforePerMeterPrice":6177.78,"afterPrice":360666.92,"afterPerMeterPrice":6011.12}]';

        $expected = [
            new PriceHistoryDto(
                new DateTime('2025-01-07 14:05:59'),
                370666.92,
                6177.78,
                360666.92,
                6011.12
            ),
        ];

        /** @When */
        $actual = $this->serviceUnderTest->createPriceHistoryFromJson($rawData);

        /** @Then */
        $this->assertEquals($expected, $actual);
    }
}